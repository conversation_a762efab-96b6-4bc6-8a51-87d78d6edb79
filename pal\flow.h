#ifndef FLOW_H
#define FLOW_H

#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include <stdbool.h>
#include "ipaddr.h"

struct flow_key {
	union ipaddr saddr;
	union ipaddr daddr;
	uint16_t sport;
	uint16_t dport;
	uint8_t proto;
	uint8_t pad[3];
};

enum {
	CLIENT_TO_SERVER = 0, /* Matches PML_MATCH_NORMAL */
	SERVER_TO_CLIENT = 1  /* Matches PML_MATCH_EXTEND */
};

static struct flow_key *
flow_key_reverse(struct flow_key *dst, struct flow_key *org)
{
	dst->proto = org->proto;
	dst->sport = org->dport;
	dst->dport = org->sport;
	ipaddr(&dst->saddr, &org->daddr, AF_INET6);
	ipaddr(&dst->daddr, &org->saddr, AF_INET6);
	return dst;
}

static inline uint64_t
flow_key_hash(struct flow_key *ptr, struct flow_key *rkey, int *rev)
{
	/* hash must be symmetric to match either direction */
	if (ptr->sport > ptr->dport || (ptr->sport == ptr->dport && memcmp(&ptr->saddr, &ptr->daddr, sizeof(ptr->daddr)) > 0))
		ptr = flow_key_reverse(rkey, ptr);

	*rev = ptr == rkey;
	return (ptr->saddr.addr64[1] ^ ptr->daddr.addr64[1]) + ptr->sport + ptr->dport + ptr->proto;
}

#endif
