/**
 * @file pointer_offset_demo.c
 * @brief 演示PML watchpoint中指针转换的原理
 */

#include <stdio.h>
#include <stddef.h>
#include <stdint.h>

/* 模拟NSA的结构体 */
struct nsa_pml_context {
    uint8_t src[16];
    uint8_t dst[16];
    uint16_t sport;
    uint16_t dport;
    uint8_t proto;
    uint8_t verdict;
    uint16_t szone;
    uint16_t dzone;
    uint16_t group;
    uint32_t user;
    uint64_t host;
    uint8_t device[6];
    uint16_t alp;
    uint16_t app;
    uint16_t dev;
    uint16_t geo;
    uint16_t threat;
    uint16_t cat;
    uint16_t date;
} __attribute__((packed));

typedef struct nsa_session_context {
    void *pml_classify_instance;        // 8 bytes
    void *pml_rules_instance;           // 8 bytes  
    struct nsa_pml_context pml_context; // 76 bytes
    uint32_t rule_state;
    uint64_t last_analysis_time;
    char server_name[256];              // 这是我们要访问的字段
    // ... 其他字段
} nsa_session_context_t;

/* 模拟PML watchpoint回调函数 */
void demo_watchpoint_callback(void *ctx) {
    printf("=== PML Watchpoint指针转换演示 ===\n\n");
    
    /* 显示接收到的指针信息 */
    printf("1. 回调函数接收到的ctx指针: %p\n", ctx);
    printf("   这个指针指向nsa_session_context中的pml_context字段\n\n");
    
    /* 计算偏移量 */
    size_t offset = offsetof(nsa_session_context_t, pml_context);
    printf("2. pml_context在nsa_session_context中的偏移量: %zu bytes\n", offset);
    
    /* 执行指针转换 */
    nsa_session_context_t *nsa_ctx = (nsa_session_context_t *)((char *)ctx - offset);
    printf("3. 转换后的nsa_session_context指针: %p\n", (void*)nsa_ctx);
    
    /* 验证转换是否正确 */
    void *calculated_pml_context = &nsa_ctx->pml_context;
    printf("4. 验证: &nsa_ctx->pml_context = %p\n", calculated_pml_context);
    printf("   原始ctx指针 = %p\n", ctx);
    printf("   指针转换%s!\n\n", (calculated_pml_context == ctx) ? "成功" : "失败");
    
    /* 现在可以安全地访问server_name字段 */
    printf("5. 现在可以访问server_name字段:\n");
    printf("   server_name地址: %p\n", (void*)nsa_ctx->server_name);
    printf("   server_name内容: \"%s\"\n", nsa_ctx->server_name);
}

/* 模拟PML引擎的行为 */
void simulate_pml_engine(nsa_session_context_t *session) {
    printf("=== 模拟PML引擎行为 ===\n\n");
    
    /* PML引擎记住传递给pml_main的指针 */
    void *saved_context = &session->pml_context;
    printf("PML引擎保存的context指针: %p\n", saved_context);
    printf("(这是通过pml_main(&session->pml_context)传递的)\n\n");
    
    /* 当检测到server_name变化时，PML引擎调用回调函数 */
    printf("检测到server_name变化，调用watchpoint回调...\n\n");
    demo_watchpoint_callback(saved_context);
}

int main() {
    /* 创建一个模拟的session context */
    nsa_session_context_t session = {0};
    
    /* 设置一些测试数据 */
    snprintf(session.server_name, sizeof(session.server_name), "www.example.com");
    
    printf("=== 内存布局信息 ===\n");
    printf("nsa_session_context大小: %zu bytes\n", sizeof(nsa_session_context_t));
    printf("nsa_pml_context大小: %zu bytes\n", sizeof(struct nsa_pml_context));
    printf("session地址: %p\n", (void*)&session);
    printf("session.pml_context地址: %p\n", (void*)&session.pml_context);
    printf("session.server_name地址: %p\n", (void*)session.server_name);
    printf("\n");
    
    /* 显示各字段的偏移量 */
    printf("=== 字段偏移量 ===\n");
    printf("pml_classify_instance偏移: %zu\n", offsetof(nsa_session_context_t, pml_classify_instance));
    printf("pml_rules_instance偏移: %zu\n", offsetof(nsa_session_context_t, pml_rules_instance));
    printf("pml_context偏移: %zu\n", offsetof(nsa_session_context_t, pml_context));
    printf("rule_state偏移: %zu\n", offsetof(nsa_session_context_t, rule_state));
    printf("server_name偏移: %zu\n", offsetof(nsa_session_context_t, server_name));
    printf("\n");
    
    /* 模拟整个过程 */
    simulate_pml_engine(&session);
    
    return 0;
}
