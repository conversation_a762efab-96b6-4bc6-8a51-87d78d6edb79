#ifndef BPF_H
#define BPF_H

#include <stdint.h>

struct bpf_insn
{
    uint8_t opcode;
    uint8_t dst : 4;
    uint8_t src : 4;
    int16_t offset;
    int32_t imm;
};

enum bpf_register
{
    BPF_REG_0 = 0,
    BPF_REG_1,
    BPF_REG_2,
    BPF_REG_3,
    BPF_REG_4,
    BPF_REG_5,
    BPF_REG_6,
    BPF_REG_7,
    BPF_REG_8,
    BPF_REG_9,
    BPF_REG_10,
    _BPF_REG_MAX,
};

typedef uint64_t (*bpf_function_t)(uint64_t, uint64_t, uint64_t, uint64_t, uint64_t, void *);

#define BPF_CLS_MASK 0x07
#define BPF_ALU_OP_MASK 0xf0
#define BPF_JMP_OP_MASK 0xf0

#define BPF_CLS_LD 0x00
#define BPF_CLS_LDX 0x01
#define BPF_CLS_ST 0x02
#define BPF_CLS_STX 0x03
#define BPF_CLS_ALU 0x04
#define BPF_CLS_JMP 0x05
#define BPF_CLS_JMP32 0x06
#define BPF_CLS_ALU64 0x07

#define BPF_SRC_IMM 0x00
#define BPF_SRC_REG 0x08

#define BPF_SIZE_W 0x00
#define BPF_SIZE_H 0x08
#define BPF_SIZE_B 0x10
#define BPF_SIZE_DW 0x18

/**
 * @brief Atomic modifier for BPF_CLS_STX operation.
 */
#define BPF_MODE_ATOMIC 0xc0

#define BPF_ATOMIC_OP_FETCH 0x01
#define BPF_ATOMIC_OP_XCHG (0xe0 | BPF_ATOMIC_OP_FETCH)
#define BPF_ATOMIC_OP_CMPXCHG (0xf0 | BPF_ATOMIC_OP_FETCH)

/* Other memory modes are not yet supported */
#define BPF_MODE_IMM 0x00
#define BPF_MODE_MEM 0x60

#define BPF_ALU_OP_ADD 0x00
#define BPF_ALU_OP_SUB 0x10
#define BPF_ALU_OP_MUL 0x20
#define BPF_ALU_OP_DIV 0x30
#define BPF_ALU_OP_OR 0x40
#define BPF_ALU_OP_AND 0x50
#define BPF_ALU_OP_LSH 0x60
#define BPF_ALU_OP_RSH 0x70
#define BPF_ALU_OP_NEG 0x80
#define BPF_ALU_OP_MOD 0x90
#define BPF_ALU_OP_XOR 0xa0
#define BPF_ALU_OP_MOV 0xb0
#define BPF_ALU_OP_ARSH 0xc0
#define BPF_ALU_OP_END 0xd0

#define BPF_OP_ADD_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_ADD)
#define BPF_OP_ADD_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_ADD)
#define BPF_OP_SUB_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_SUB)
#define BPF_OP_SUB_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_SUB)
#define BPF_OP_MUL_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_MUL)
#define BPF_OP_MUL_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_MUL)
#define BPF_OP_DIV_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_DIV)
#define BPF_OP_DIV_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_DIV)
#define BPF_OP_OR_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_OR)
#define BPF_OP_OR_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_OR)
#define BPF_OP_AND_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_AND)
#define BPF_OP_AND_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_AND)
#define BPF_OP_LSH_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_LSH)
#define BPF_OP_LSH_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_LSH)
#define BPF_OP_RSH_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_RSH)
#define BPF_OP_RSH_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_RSH)
#define BPF_OP_NEG (BPF_CLS_ALU | BPF_ALU_OP_NEG)
#define BPF_OP_MOD_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_MOD)
#define BPF_OP_MOD_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_MOD)
#define BPF_OP_XOR_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_XOR)
#define BPF_OP_XOR_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_XOR)
#define BPF_OP_MOV_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_MOV)
#define BPF_OP_MOV_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_MOV)
#define BPF_OP_ARSH_IMM (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_ARSH)
#define BPF_OP_ARSH_REG (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_ARSH)
#define BPF_OP_LE (BPF_CLS_ALU | BPF_SRC_IMM | BPF_ALU_OP_END)
#define BPF_OP_BE (BPF_CLS_ALU | BPF_SRC_REG | BPF_ALU_OP_END)

#define BPF_OP_ADD64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_ADD)
#define BPF_OP_ADD64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_ADD)
#define BPF_OP_SUB64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_SUB)
#define BPF_OP_SUB64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_SUB)
#define BPF_OP_MUL64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_MUL)
#define BPF_OP_MUL64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_MUL)
#define BPF_OP_DIV64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_DIV)
#define BPF_OP_DIV64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_DIV)
#define BPF_OP_OR64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_OR)
#define BPF_OP_OR64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_OR)
#define BPF_OP_AND64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_AND)
#define BPF_OP_AND64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_AND)
#define BPF_OP_LSH64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_LSH)
#define BPF_OP_LSH64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_LSH)
#define BPF_OP_RSH64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_RSH)
#define BPF_OP_RSH64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_RSH)
#define BPF_OP_NEG64 (BPF_CLS_ALU64 | BPF_ALU_OP_NEG)
#define BPF_OP_MOD64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_MOD)
#define BPF_OP_MOD64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_MOD)
#define BPF_OP_XOR64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_XOR)
#define BPF_OP_XOR64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_XOR)
#define BPF_OP_MOV64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_MOV)
#define BPF_OP_MOV64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_MOV)
#define BPF_OP_ARSH64_IMM (BPF_CLS_ALU64 | BPF_SRC_IMM | BPF_ALU_OP_ARSH)
#define BPF_OP_ARSH64_REG (BPF_CLS_ALU64 | BPF_SRC_REG | BPF_ALU_OP_ARSH)

#define BPF_OP_LDXW (BPF_CLS_LDX | BPF_MODE_MEM | BPF_SIZE_W)
#define BPF_OP_LDXH (BPF_CLS_LDX | BPF_MODE_MEM | BPF_SIZE_H)
#define BPF_OP_LDXB (BPF_CLS_LDX | BPF_MODE_MEM | BPF_SIZE_B)
#define BPF_OP_LDXDW (BPF_CLS_LDX | BPF_MODE_MEM | BPF_SIZE_DW)
#define BPF_OP_STW (BPF_CLS_ST | BPF_MODE_MEM | BPF_SIZE_W)
#define BPF_OP_STH (BPF_CLS_ST | BPF_MODE_MEM | BPF_SIZE_H)
#define BPF_OP_STB (BPF_CLS_ST | BPF_MODE_MEM | BPF_SIZE_B)
#define BPF_OP_STDW (BPF_CLS_ST | BPF_MODE_MEM | BPF_SIZE_DW)
#define BPF_OP_STXW (BPF_CLS_STX | BPF_MODE_MEM | BPF_SIZE_W)
#define BPF_OP_STXH (BPF_CLS_STX | BPF_MODE_MEM | BPF_SIZE_H)
#define BPF_OP_STXB (BPF_CLS_STX | BPF_MODE_MEM | BPF_SIZE_B)
#define BPF_OP_STXDW (BPF_CLS_STX | BPF_MODE_MEM | BPF_SIZE_DW)
#define BPF_OP_LDDW (BPF_CLS_LD | BPF_MODE_IMM | BPF_SIZE_DW)

#define BPF_MODE_JA 0x00
#define BPF_MODE_JEQ 0x10
#define BPF_MODE_JGT 0x20
#define BPF_MODE_JGE 0x30
#define BPF_MODE_JSET 0x40
#define BPF_MODE_JNE 0x50
#define BPF_MODE_JSGT 0x60
#define BPF_MODE_JSGE 0x70
#define BPF_MODE_CALL 0x80
#define BPF_MODE_EXIT 0x90
#define BPF_MODE_JLT 0xa0
#define BPF_MODE_JLE 0xb0
#define BPF_MODE_JSLT 0xc0
#define BPF_MODE_JSLE 0xd0

#define BPF_OP_JA (BPF_CLS_JMP | BPF_MODE_JA)
#define BPF_OP_JEQ_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JEQ)
#define BPF_OP_JEQ_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JEQ)
#define BPF_OP_JGT_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JGT)
#define BPF_OP_JGT_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JGT)
#define BPF_OP_JGE_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JGE)
#define BPF_OP_JGE_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JGE)
#define BPF_OP_JSET_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JSET)
#define BPF_OP_JSET_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JSET)
#define BPF_OP_JNE_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JNE)
#define BPF_OP_JNE_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JNE)
#define BPF_OP_JSGT_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JSGT)
#define BPF_OP_JSGT_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JSGT)
#define BPF_OP_JSGE_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JSGE)
#define BPF_OP_JSGE_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JSGE)
#define BPF_OP_CALL (BPF_CLS_JMP | BPF_MODE_CALL)
#define BPF_OP_EXIT (BPF_CLS_JMP | BPF_MODE_EXIT)
#define BPF_OP_JLT_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JLT)
#define BPF_OP_JLT_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JLT)
#define BPF_OP_JLE_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JLE)
#define BPF_OP_JLE_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JLE)
#define BPF_OP_JSLT_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JSLT)
#define BPF_OP_JSLT_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JSLT)
#define BPF_OP_JSLE_IMM (BPF_CLS_JMP | BPF_SRC_IMM | BPF_MODE_JSLE)
#define BPF_OP_JSLE_REG (BPF_CLS_JMP | BPF_SRC_REG | BPF_MODE_JSLE)

#define BPF_OP_JEQ32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JEQ)
#define BPF_OP_JEQ32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JEQ)
#define BPF_OP_JGT32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JGT)
#define BPF_OP_JGT32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JGT)
#define BPF_OP_JGE32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JGE)
#define BPF_OP_JGE32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JGE)
#define BPF_OP_JSET32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JSET)
#define BPF_OP_JSET32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JSET)
#define BPF_OP_JNE32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JNE)
#define BPF_OP_JNE32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JNE)
#define BPF_OP_JSGT32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JSGT)
#define BPF_OP_JSGT32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JSGT)
#define BPF_OP_JSGE32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JSGE)
#define BPF_OP_JSGE32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JSGE)
#define BPF_OP_JLT32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JLT)
#define BPF_OP_JLT32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JLT)
#define BPF_OP_JLE32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JLE)
#define BPF_OP_JLE32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JLE)
#define BPF_OP_JSLT32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JSLT)
#define BPF_OP_JSLT32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JSLT)
#define BPF_OP_JSLE32_IMM (BPF_CLS_JMP32 | BPF_SRC_IMM | BPF_MODE_JSLE)
#define BPF_OP_JSLE32_REG (BPF_CLS_JMP32 | BPF_SRC_REG | BPF_MODE_JSLE)

#define BPF_OP_ATOMIC32_STORE (BPF_CLS_STX | BPF_MODE_ATOMIC | BPF_SIZE_W)
#define BPF_OP_ATOMIC_STORE (BPF_CLS_STX | BPF_MODE_ATOMIC | BPF_SIZE_DW)

int bpf_register(bpf_function_t f, uint8_t index);

uint64_t bpf_prog_run(uint64_t *reg, const struct bpf_insn *insn, void *arg);

#define BPF_PROG_RUN(name, stack_size) \
static uint64_t name(const void *ctx, const struct bpf_insn *insn, void *arg) \
{ \
	uint64_t stack[stack_size / sizeof(uint64_t)]; \
	uint64_t reg[16] = {0}; \
	reg[1]  = (uintptr_t)ctx; \
	reg[10] = (uintptr_t)&stack[ARRAY_SIZE(stack)]; \
	return bpf_prog_run(reg, insn, arg); \
}	

#endif
