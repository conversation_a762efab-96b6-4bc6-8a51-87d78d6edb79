#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <endian.h>

#include "bpf.h"

static bpf_function_t bpf_call[256];

int bpf_register(bpf_function_t f, uint8_t index)
{
	bpf_call[index] = f;
	return 0;
}

static inline uint64_t
bpf_mem_load(uint64_t address, size_t size)
{
	uint64_t value = 0;
	const uint8_t *from = (const uint8_t *)(uintptr_t)address;
	memcpy(&value, from, size);
	return value;
}

static inline void
bpf_mem_store(uint64_t address, uint64_t value, size_t size)
{
	uint8_t *to = (uint8_t *)(uintptr_t)address;
	memcpy(to, &value, size);
}

static uint32_t
u32(uint64_t x)
{
    return x;
}

static int32_t
i32(uint64_t x)
{
    return x;
}

static int64_t
i64(int32_t x)
{
	return (int64_t)x;
}

void
dump_stack(uint8_t *stack, size_t size)
{
	uint8_t *ptr = stack + size;
	while (ptr > stack) {
		ptr -= 8;
		printf("%02x%02x%02x%02x%02x%02x%02x%02x\n",
				ptr[0], ptr[1], ptr[2], ptr[3],
				ptr[4], ptr[5], ptr[6], ptr[7]);
	}
}

uint64_t
bpf_prog_run(uint64_t *reg, const struct bpf_insn *inst, void *arg)
{
	static const void *jump_table[] = {
		[0 ... 255] = &&default_label,

		[BPF_OP_ADD_IMM] = &&bpf_op_add_imm,
		[BPF_OP_ADD_REG] = &&bpf_op_add_reg,
		[BPF_OP_SUB_IMM] = &&bpf_op_sub_imm,
		[BPF_OP_SUB_REG] = &&bpf_op_sub_reg,
		[BPF_OP_MUL_IMM] = &&bpf_op_mul_imm,
		[BPF_OP_MUL_REG] = &&bpf_op_mul_reg,
		[BPF_OP_DIV_IMM] = &&bpf_op_div_imm,
		[BPF_OP_DIV_REG] = &&bpf_op_div_reg,
		[BPF_OP_OR_IMM] = &&bpf_op_or_imm,
		[BPF_OP_OR_REG] = &&bpf_op_or_reg,
		[BPF_OP_AND_IMM] = &&bpf_op_and_imm,
		[BPF_OP_AND_REG] = &&bpf_op_and_reg,
		[BPF_OP_LSH_IMM] = &&bpf_op_lsh_imm,
		[BPF_OP_LSH_REG] = &&bpf_op_lsh_reg,
		[BPF_OP_RSH_IMM] = &&bpf_op_rsh_imm,
		[BPF_OP_RSH_REG] = &&bpf_op_rsh_reg,
		[BPF_OP_NEG] = &&bpf_op_neg,
		[BPF_OP_MOD_IMM] = &&bpf_op_mod_imm,
		[BPF_OP_MOD_REG] = &&bpf_op_mod_reg,
		[BPF_OP_XOR_IMM] = &&bpf_op_xor_imm,
		[BPF_OP_XOR_REG] = &&bpf_op_xor_reg,
		[BPF_OP_MOV_IMM] = &&bpf_op_mov_imm,
		[BPF_OP_MOV_REG] = &&bpf_op_mov_reg,
		[BPF_OP_ARSH_IMM] = &&bpf_op_arsh_imm,
		[BPF_OP_ARSH_REG] = &&bpf_op_arsh_reg,
		[BPF_OP_LE] = &&bpf_op_le,
		[BPF_OP_BE] = &&bpf_op_be,
		[BPF_OP_ADD64_IMM] = &&bpf_op_add64_imm,
		[BPF_OP_ADD64_REG] = &&bpf_op_add64_reg,
		[BPF_OP_SUB64_IMM] = &&bpf_op_sub64_imm,
		[BPF_OP_SUB64_REG] = &&bpf_op_sub64_reg,
		[BPF_OP_MUL64_IMM] = &&bpf_op_mul64_imm,
		[BPF_OP_MUL64_REG] = &&bpf_op_mul64_reg,
		[BPF_OP_DIV64_IMM] = &&bpf_op_div64_imm,
		[BPF_OP_DIV64_REG] = &&bpf_op_div64_reg,
		[BPF_OP_OR64_IMM] = &&bpf_op_or64_imm,
		[BPF_OP_OR64_REG] = &&bpf_op_or64_reg,
		[BPF_OP_AND64_IMM] = &&bpf_op_and64_imm,
		[BPF_OP_AND64_REG] = &&bpf_op_and64_reg,
		[BPF_OP_LSH64_IMM] = &&bpf_op_lsh64_imm,
		[BPF_OP_LSH64_REG] = &&bpf_op_lsh64_reg,
		[BPF_OP_RSH64_IMM] = &&bpf_op_rsh64_imm,
		[BPF_OP_RSH64_REG] = &&bpf_op_rsh64_reg,
		[BPF_OP_NEG64] = &&bpf_op_neg64,
		[BPF_OP_MOD64_IMM] = &&bpf_op_mod64_imm,
		[BPF_OP_MOD64_REG] = &&bpf_op_mod64_reg,
		[BPF_OP_XOR64_IMM] = &&bpf_op_xor64_imm,
		[BPF_OP_XOR64_REG] = &&bpf_op_xor64_reg,
		[BPF_OP_MOV64_IMM] = &&bpf_op_mov64_imm,
		[BPF_OP_MOV64_REG] = &&bpf_op_mov64_reg,
		[BPF_OP_ARSH64_IMM] = &&bpf_op_arsh64_imm,
		[BPF_OP_ARSH64_REG] = &&bpf_op_arsh64_reg,
		[BPF_OP_LDXW] = &&bpf_op_ldxw,
		[BPF_OP_LDXH] = &&bpf_op_ldxh,
		[BPF_OP_LDXB] = &&bpf_op_ldxb,
		[BPF_OP_LDXDW] = &&bpf_op_ldxdw,
		[BPF_OP_STW] = &&bpf_op_stw,
		[BPF_OP_STH] = &&bpf_op_sth,
		[BPF_OP_STB] = &&bpf_op_stb,
		[BPF_OP_STDW] = &&bpf_op_stdw,
		[BPF_OP_STXW] = &&bpf_op_stxw,
		[BPF_OP_STXH] = &&bpf_op_stxh,
		[BPF_OP_STXB] = &&bpf_op_stxb,
		[BPF_OP_STXDW] = &&bpf_op_stxdw,
		[BPF_OP_LDDW] = &&bpf_op_lddw,
		[BPF_OP_JA] = &&bpf_op_ja,
		[BPF_OP_JEQ_IMM] = &&bpf_op_jeq_imm,
		[BPF_OP_JEQ_REG] = &&bpf_op_jeq_reg,
		[BPF_OP_JEQ32_IMM] = &&bpf_op_jeq32_imm,
		[BPF_OP_JEQ32_REG] = &&bpf_op_jeq32_reg,
		[BPF_OP_JGT_IMM] = &&bpf_op_jgt_imm,
		[BPF_OP_JGT_REG] = &&bpf_op_jgt_reg,
		[BPF_OP_JGT32_IMM] = &&bpf_op_jgt32_imm,
		[BPF_OP_JGT32_REG] = &&bpf_op_jgt32_reg,
		[BPF_OP_JGE_IMM] = &&bpf_op_jge_imm,
		[BPF_OP_JGE_REG] = &&bpf_op_jge_reg,
		[BPF_OP_JGE32_IMM] = &&bpf_op_jge32_imm,
		[BPF_OP_JGE32_REG] = &&bpf_op_jge32_reg,
		[BPF_OP_JLT_IMM] = &&bpf_op_jlt_imm,
		[BPF_OP_JLT_REG] = &&bpf_op_jlt_reg,
		[BPF_OP_JLT32_IMM] = &&bpf_op_jlt32_imm,
		[BPF_OP_JLT32_REG] = &&bpf_op_jlt32_reg,
		[BPF_OP_JLE_IMM] = &&bpf_op_jle_imm,
		[BPF_OP_JLE_REG] = &&bpf_op_jle_reg,
		[BPF_OP_JLE32_IMM] = &&bpf_op_jle32_imm,
		[BPF_OP_JLE32_REG] = &&bpf_op_jle32_reg,
		[BPF_OP_JSET_IMM] = &&bpf_op_jset_imm,
		[BPF_OP_JSET_REG] = &&bpf_op_jset_reg,
		[BPF_OP_JSET32_IMM] = &&bpf_op_jset32_imm,
		[BPF_OP_JSET32_REG] = &&bpf_op_jset32_reg,
		[BPF_OP_JNE_IMM] = &&bpf_op_jne_imm,
		[BPF_OP_JNE_REG] = &&bpf_op_jne_reg,
		[BPF_OP_JNE32_IMM] = &&bpf_op_jne32_imm,
		[BPF_OP_JNE32_REG] = &&bpf_op_jne32_reg,
		[BPF_OP_JSGT_IMM] = &&bpf_op_jsgt_imm,
		[BPF_OP_JSGT_REG] = &&bpf_op_jsgt_reg,
		[BPF_OP_JSGT32_IMM] = &&bpf_op_jsgt32_imm,
		[BPF_OP_JSGT32_REG] = &&bpf_op_jsgt32_reg,
		[BPF_OP_JSGE_IMM] = &&bpf_op_jsge_imm,
		[BPF_OP_JSGE_REG] = &&bpf_op_jsge_reg,
		[BPF_OP_JSGE32_IMM] = &&bpf_op_jsge32_imm,
		[BPF_OP_JSGE32_REG] = &&bpf_op_jsge32_reg,
		[BPF_OP_JSLT_IMM] = &&bpf_op_jslt_imm,
		[BPF_OP_JSLT_REG] = &&bpf_op_jslt_reg,
		[BPF_OP_JSLT32_IMM] = &&bpf_op_jslt32_imm,
		[BPF_OP_JSLT32_REG] = &&bpf_op_jslt32_reg,
		[BPF_OP_JSLE_IMM] = &&bpf_op_jsle_imm,
		[BPF_OP_JSLE_REG] = &&bpf_op_jsle_reg,
		[BPF_OP_JSLE32_IMM] = &&bpf_op_jsle32_imm,
		[BPF_OP_JSLE32_REG] = &&bpf_op_jsle32_reg,
		[BPF_OP_EXIT] = &&bpf_op_exit,
		[BPF_OP_CALL] = &&bpf_op_call,
		[BPF_OP_ATOMIC_STORE] = &&bpf_op_atomic_store,
		[BPF_OP_ATOMIC32_STORE] = &&bpf_op_atomic32_store
	};

	goto *jump_table[inst->opcode];

#define CONT	({ inst++; goto *jump_table[inst->opcode]; })

	bpf_op_add_imm:
		reg[inst->dst] += inst->imm;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_add_reg:
		reg[inst->dst] += reg[inst->src];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_sub_imm:
		reg[inst->dst] -= inst->imm;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_sub_reg:
		reg[inst->dst] -= reg[inst->src];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_mul_imm:
		reg[inst->dst] *= inst->imm;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_mul_reg:
		reg[inst->dst] *= reg[inst->src];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_div_imm:
		reg[inst->dst] = u32(inst->imm) ? u32(reg[inst->dst]) / u32(inst->imm) : 0;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_div_reg:
		reg[inst->dst] = u32(reg[inst->src]) ? u32(reg[inst->dst]) / u32(reg[inst->src]) : 0;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_or_imm:
		reg[inst->dst] |= inst->imm;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_or_reg:
		reg[inst->dst] |= reg[inst->src];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_and_imm:
		reg[inst->dst] &= inst->imm;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_and_reg:
		reg[inst->dst] &= reg[inst->src];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
#define SHIFT_MASK_32_BIT(X) ((X) & 0x1f)
		bpf_op_lsh_imm:
		reg[inst->dst] = (u32(reg[inst->dst]) << SHIFT_MASK_32_BIT(inst->imm) & UINT32_MAX);
		CONT;
	bpf_op_lsh_reg:
		reg[inst->dst] = (u32(reg[inst->dst]) << SHIFT_MASK_32_BIT(reg[inst->src]) & UINT32_MAX);
		CONT;
	bpf_op_rsh_imm:
		reg[inst->dst] = u32(reg[inst->dst]) >> SHIFT_MASK_32_BIT(inst->imm);
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_rsh_reg:
		reg[inst->dst] = u32(reg[inst->dst]) >> SHIFT_MASK_32_BIT(reg[inst->src]);
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_neg:
		reg[inst->dst] = -(int64_t)reg[inst->dst];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_mod_imm:
		reg[inst->dst] = u32(inst->imm) ? u32(reg[inst->dst]) % u32(inst->imm) : u32(reg[inst->dst]);
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_mod_reg:
		reg[inst->dst] = u32(reg[inst->src]) ? u32(reg[inst->dst]) % u32(reg[inst->src]) : u32(reg[inst->dst]);
		CONT;
	bpf_op_xor_imm:
		reg[inst->dst] ^= inst->imm;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_xor_reg:
		reg[inst->dst] ^= reg[inst->src];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_mov_imm:
		reg[inst->dst] = inst->imm;
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_mov_reg:
		reg[inst->dst] = reg[inst->src];
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_arsh_imm:
		reg[inst->dst] = (int32_t)reg[inst->dst] >> SHIFT_MASK_32_BIT(inst->imm);
		reg[inst->dst] &= UINT32_MAX;
		CONT;
	bpf_op_arsh_reg:
		reg[inst->dst] = (int32_t)reg[inst->dst] >> SHIFT_MASK_32_BIT(reg[inst->src]);
		reg[inst->dst] &= UINT32_MAX;
		CONT;

	bpf_op_le:
		if (inst->imm == 16) {
			reg[inst->dst] = htole16(reg[inst->dst]);
		} else if (inst->imm == 32) {
			reg[inst->dst] = htole32(reg[inst->dst]);
		} else if (inst->imm == 64) {
			reg[inst->dst] = htole64(reg[inst->dst]);
		}
		CONT;
	bpf_op_be:
		if (inst->imm == 16) {
			reg[inst->dst] = htobe16(reg[inst->dst]);
		} else if (inst->imm == 32) {
			reg[inst->dst] = htobe32(reg[inst->dst]);
		} else if (inst->imm == 64) {
			reg[inst->dst] = htobe64(reg[inst->dst]);
		}
		CONT;

	bpf_op_add64_imm:
		reg[inst->dst] += inst->imm;
		CONT;
	bpf_op_add64_reg:
		reg[inst->dst] += reg[inst->src];
		CONT;
	bpf_op_sub64_imm:
		reg[inst->dst] -= inst->imm;
		CONT;
	bpf_op_sub64_reg:
		reg[inst->dst] -= reg[inst->src];
		CONT;
	bpf_op_mul64_imm:
		reg[inst->dst] *= inst->imm;
		CONT;
	bpf_op_mul64_reg:
		reg[inst->dst] *= reg[inst->src];
		CONT;
	bpf_op_div64_imm:
		reg[inst->dst] = inst->imm ? reg[inst->dst] / inst->imm : 0;
		CONT;
	bpf_op_div64_reg:
		reg[inst->dst] = reg[inst->src] ? reg[inst->dst] / reg[inst->src] : 0;
		CONT;
	bpf_op_or64_imm:
		reg[inst->dst] |= inst->imm;
		CONT;
	bpf_op_or64_reg:
		reg[inst->dst] |= reg[inst->src];
		CONT;
	bpf_op_and64_imm:
		reg[inst->dst] &= inst->imm;
		CONT;
	bpf_op_and64_reg:
		reg[inst->dst] &= reg[inst->src];
		CONT;
#define SHIFT_MASK_64_BIT(X) ((X) & 0x3f)
	bpf_op_lsh64_imm:
		reg[inst->dst] <<= SHIFT_MASK_64_BIT(inst->imm);
		CONT;
	bpf_op_lsh64_reg:
		reg[inst->dst] <<= SHIFT_MASK_64_BIT(reg[inst->src]);
		CONT;
	bpf_op_rsh64_imm:
		reg[inst->dst] >>= SHIFT_MASK_64_BIT(inst->imm);
		CONT;
	bpf_op_rsh64_reg:
		reg[inst->dst] >>= SHIFT_MASK_64_BIT(reg[inst->src]);
		CONT;
	bpf_op_neg64:
		reg[inst->dst] = -reg[inst->dst];
		CONT;
	bpf_op_mod64_imm:
		reg[inst->dst] = inst->imm ? reg[inst->dst] % inst->imm : reg[inst->dst];
		CONT;
	bpf_op_mod64_reg:
		reg[inst->dst] = reg[inst->src] ? reg[inst->dst] % reg[inst->src] : reg[inst->dst];
		CONT;
	bpf_op_xor64_imm:
		reg[inst->dst] ^= inst->imm;
		CONT;
	bpf_op_xor64_reg:
		reg[inst->dst] ^= reg[inst->src];
		CONT;
	bpf_op_mov64_imm:
		reg[inst->dst] = inst->imm;
		CONT;
	bpf_op_mov64_reg:
		reg[inst->dst] = reg[inst->src];
		CONT;
	bpf_op_arsh64_imm:
		reg[inst->dst] = (int64_t)reg[inst->dst] >> SHIFT_MASK_64_BIT(inst->imm);
		CONT;
	bpf_op_arsh64_reg:
		reg[inst->dst] = (int64_t)reg[inst->dst] >> SHIFT_MASK_64_BIT(reg[inst->src]);
		CONT;

	bpf_op_ldxw:
		reg[inst->dst] = bpf_mem_load(reg[inst->src] + inst->offset, 4);
		CONT;
	bpf_op_ldxh:
		reg[inst->dst] = bpf_mem_load(reg[inst->src] + inst->offset, 2);
		CONT;
	bpf_op_ldxb:
		reg[inst->dst] = bpf_mem_load(reg[inst->src] + inst->offset, 1);
		CONT;
	bpf_op_ldxdw:
		reg[inst->dst] = bpf_mem_load(reg[inst->src] + inst->offset, 8);
		CONT;

	bpf_op_stw:
		bpf_mem_store(reg[inst->dst] + inst->offset, inst->imm, 4);
		CONT;
	bpf_op_sth:
		bpf_mem_store(reg[inst->dst] + inst->offset, inst->imm, 2);
		CONT;
	bpf_op_stb:
		bpf_mem_store(reg[inst->dst] + inst->offset, inst->imm, 1);
		CONT;
	bpf_op_stdw:
		bpf_mem_store(reg[inst->dst] + inst->offset, inst->imm, 8);
		CONT;

	bpf_op_stxw:
		bpf_mem_store(reg[inst->dst] + inst->offset, reg[inst->src], 4);
		CONT;
	bpf_op_stxh:
		bpf_mem_store(reg[inst->dst] + inst->offset, reg[inst->src], 2);
		CONT;
	bpf_op_stxb:
		bpf_mem_store(reg[inst->dst] + inst->offset, reg[inst->src], 1);
		CONT;
	bpf_op_stxdw:
		bpf_mem_store(reg[inst->dst] + inst->offset, reg[inst->src], 8);
		CONT;

	bpf_op_lddw:
		//	reg[inst->dst] = u32(inst->imm) | ((uint64_t)bpf_fetch_instruction(vm, inst++).imm << 32);
		//	CONT;
		assert(0);
		return -1;

	bpf_op_ja:
		inst += inst->offset;
		CONT;
	bpf_op_jeq_imm:
		if (reg[inst->dst] == (uint64_t)i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jeq_reg:
		if (reg[inst->dst] == reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jeq32_imm:
		if (u32(reg[inst->dst]) == u32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jeq32_reg:
		if (u32(reg[inst->dst]) == u32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jgt_imm:
		if (reg[inst->dst] > (uint64_t)i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jgt_reg:
		if (reg[inst->dst] > reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jgt32_imm:
		if (u32(reg[inst->dst]) > u32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jgt32_reg:
		if (u32(reg[inst->dst]) > u32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jge_imm:
		if (reg[inst->dst] >= (uint64_t)i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jge_reg:
		if (reg[inst->dst] >= reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jge32_imm:
		if (u32(reg[inst->dst]) >= u32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jge32_reg:
		if (u32(reg[inst->dst]) >= u32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jlt_imm:
		if (reg[inst->dst] < (uint64_t)i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jlt_reg:
		if (reg[inst->dst] < reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jlt32_imm:
		if (u32(reg[inst->dst]) < u32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jlt32_reg:
		if (u32(reg[inst->dst]) < u32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jle_imm:
		if (reg[inst->dst] <= (uint64_t)i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jle_reg:
		if (reg[inst->dst] <= reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jle32_imm:
		if (u32(reg[inst->dst]) <= u32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jle32_reg:
		if (u32(reg[inst->dst]) <= u32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jset_imm:
		if (reg[inst->dst] & (uint64_t)i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jset_reg:
		if (reg[inst->dst] & reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jset32_imm:
		if (u32(reg[inst->dst]) & u32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jset32_reg:
		if (u32(reg[inst->dst]) & u32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jne_imm:
		if (reg[inst->dst] != (uint64_t)i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jne_reg:
		if (reg[inst->dst] != reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jne32_imm:
		if (u32(reg[inst->dst]) != u32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jne32_reg:
		if (u32(reg[inst->dst]) != u32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsgt_imm:
		if ((int64_t)reg[inst->dst] > i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsgt_reg:
		if ((int64_t)reg[inst->dst] > (int64_t)reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsgt32_imm:
		if (i32(reg[inst->dst]) > i32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsgt32_reg:
		if (i32(reg[inst->dst]) > i32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsge_imm:
		if ((int64_t)reg[inst->dst] >= i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsge_reg:
		if ((int64_t)reg[inst->dst] >= (int64_t)reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsge32_imm:
		if (i32(reg[inst->dst]) >= i32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsge32_reg:
		if (i32(reg[inst->dst]) >= i32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jslt_imm:
		if ((int64_t)reg[inst->dst] < i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jslt_reg:
		if ((int64_t)reg[inst->dst] < (int64_t)reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jslt32_imm:
		if (i32(reg[inst->dst]) < i32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jslt32_reg:
		if (i32(reg[inst->dst]) < i32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsle_imm:
		if ((int64_t)reg[inst->dst] <= i64(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsle_reg:
		if ((int64_t)reg[inst->dst] <= (int64_t)reg[inst->src]) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsle32_imm:
		if (i32(reg[inst->dst]) <= i32(inst->imm)) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_jsle32_reg:
		if (i32(reg[inst->dst]) <= i32(reg[inst->src])) {
			inst += inst->offset;
		}
		CONT;
	bpf_op_exit:
		return reg[0];
	bpf_op_call:
		reg[0] = bpf_call[inst->imm](reg[1], reg[2], reg[3], reg[4], reg[5], arg);
		CONT;
	bpf_op_atomic_store:
	bpf_op_atomic32_store:
		assert(0);
		return -1;
	default_label:
		fprintf(stderr, "Error: unknown opcode %d\n", inst->opcode);
		assert(0);
		return -1;

}
