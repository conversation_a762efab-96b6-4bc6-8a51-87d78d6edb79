cmake_minimum_required(VERSION 3.10)

project(pal VERSION 0.0.1)

configure_file(config.h.in config.h)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -g")

# Define a custom build type for Asan
set(CMAKE_C_FLAGS_ASAN "-g -O0 -fsanitize=address,undefined -fno-omit-frame-pointer")
set(CMAKE_CONFIGURATION_TYPES "$CMAKE_CONFIGURATION_TYPES,Asan")
set(CMAKE_C_FLAGS_ASAN "${CMAKE_C_FLAGS_ASAN}" CACHE STRING "Flags for Asan build type" FORCE)
set(CMAKE_EXE_LINKER_FLAGS_ASAN "${CMAKE_EXE_LINKER_FLAGS_ASAN} -fsanitize=address,undefined" CACHE STRING "Linker flags for Asan build type" FORCE)

add_executable(pal main.c cli.c ct.c device.c driver.c mmap.c packet.c pcap.c rand.c classify.c control.c filter.c report.c)
target_include_directories(pal PUBLIC ${PROJECT_BINARY_DIR})
target_link_libraries(pal pcap ev readline pml)

set_target_properties(pal PROPERTIES OUTPUT_NAME pal)

install(TARGETS pal DESTINATION bin)

execute_process(
	COMMAND ${CMAKE_C_COMPILER} -dumpmachine
	OUTPUT_VARIABLE target
	OUTPUT_STRIP_TRAILING_WHITESPACE
)

set(CPACK_GENERATOR "DEB")
set(CPACK_DEBIAN_PACKAGE_MAINTAINER "Austen")
set(CPACK_SYSTEM_NAME ${target})
include(CPack)
