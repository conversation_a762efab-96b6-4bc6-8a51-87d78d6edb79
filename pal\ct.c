#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <getopt.h>

#include <pml/pml.h>

#include "module.h"
#include "ct.h"
#include "cli.h"
#include "classify.h"
#include "control.h"

#define CT_SIZE (4096)
#define CT_MASK (CT_SIZE - 1)

// String size + null termination
static const size_t MAC_ADDR_STR_LEN = 18;

static struct list_head lru;
static struct list_head table[CT_SIZE];

static inline bool
ct_equal(struct conn *conn, const struct flow_key *key)
{
	/* Size of a struct flow_key up to, and including, @proto */
	static const size_t cmplen = 37;
	return !memcmp(&conn->key, key, cmplen);
}

int
ct_upsert(struct flow_key *key, uint8_t *dir, struct conn **cp, const uint64_t tick)
{
	int res = 0;
	static uint32_t id;
	struct conn *conn;
	struct flow_key rkey;
	int rev;
	uint64_t hash = flow_key_hash(key, &rkey, &rev);
	const struct flow_key *fkey = rev ? &rkey : key;
	struct list_head *list = &table[hash & CT_MASK];
	list_for_each_entry(conn, list, list) {
		if (ct_equal(conn, fkey)) {
			list_remove(&conn->lru);
			if (rev) {
				if (conn->flags & F_REVERSED)
					*dir = CLIENT_TO_SERVER;
				else
					*dir = SERVER_TO_CLIENT;
			} else {
				if (conn->flags & F_REVERSED)
					*dir = SERVER_TO_CLIENT;
				else
					*dir = CLIENT_TO_SERVER;
			}
			goto out;
		}
	}
	conn = calloc(1, sizeof(*conn));
	memcpy(&conn->ctx, key, sizeof(*key));
	list_insert(list, &conn->list);

	conn->id = ++id;
	conn->start = tick;
	memcpy(&conn->key, fkey, sizeof(conn->key));
	conn->flags |= (rev ? F_REVERSED : 0);
	*dir = 0;
	res = 1;
out:
	list_insert(&lru, &conn->lru);
	conn->touched = tick;
	*cp = conn;
	return res;
}

#define SECONDS_ELAPSED(old, new) (((new) - (old)) / 1000)
#define SECONDS_SINCE(touched, now) SECONDS_ELAPSED((touched), (now))

void
ct_expire(const uint64_t tick)
{
	struct conn *item, *tmp;
	list_for_each_entry_safe(item, tmp, &lru, lru) {
		if (SECONDS_SINCE(item->touched, tick) < 60)
			break;
		list_remove(&item->list);
		list_remove(&item->lru);
		classify_stop(item);
		free(item);
	}
}

void
ct_clear()
{
	struct conn *item, *tmp;
	list_for_each_entry_safe(item, tmp, &lru, lru) {
		list_remove(&item->list);
		list_remove(&item->lru);
		classify_stop(item);
		free(item);
	}
}

static char *
format_macaddr(uint8_t mac[6], char *buf, size_t size)
{
	snprintf(buf, size, "%02x:%02x:%02x:%02x:%02x:%02x",
			mac[0],
			mac[1],
			mac[2],
			mac[3],
			mac[4],
			mac[5]
	);
	return buf;
}

static const char *
verdict_string(uint8_t verdict)
{
	switch (verdict) {
		case VERDICT_NONE:
			return "none";
		case VERDICT_ALLOW:
			return "allow";
		case VERDICT_DENY:
			return "deny";
		case VERDICT_DROP:
			return "drop";
		default:
			assert(0);
			return "unknown";
	}
}

static int
ct_help(int argc, char *argv[])
{
	(void) argc;
	(void) argv;
	printf("  ct help             -- display this menu\n");
	printf("  ct show             -- display the conntrack table\n");
//	printf("  ct conn             -- display connection information\n");
	return 0;
}

static int
ct_show(int argc, char *argv[])
{
	uint32_t id = 0;
	int opt;
	int proto = 0;
	int sport = 0;
	int dport = 0;
	struct conn *c;

	static struct option long_options[] = {
		{ "id",        required_argument, NULL, 'I' },
		{ "sport",     required_argument, NULL, 'S' },
		{ "dport",     required_argument, NULL, 'D' },
		{ "protocol",  required_argument, NULL, 'p' },
		{ 0,           0,                 0,     0  }
	};

	int option_index = 0;
	optind = 1;
	while (1) {
		opt = getopt_long(argc, argv, "I:S:D:p:", long_options, &option_index);
		if (opt == -1)
			break;
		switch (opt) {
		case 'I':
			id = atoi(optarg);
			break;
		case 'S':
			sport = atoi(optarg);
			break;
		case 'D':
			dport = atoi(optarg);
			break;
		case 'p':
			proto = atoi(optarg);
			break;
		case '?':
		case ':':
			return 0;
		default:
			break;
		}
	}


	if (optind < argc) {
		printf("unrecognized arguments: ");
		while (optind < argc)
			printf("%s ", argv[optind++]);
		printf("\n");
		return 0;
	}

	list_for_each_entry_reverse(c, &lru, lru) {
		int alp_len = 3;
		int app_len = 3;
		int dev_len = 3;
		int geo_len = 3;
		int threat_len = 3;
		int cat_len = 3;
		const char *alp = "nil";
		const char *app = "nil";
		const char *dev = "nil";
		const char *geo = "nil";
		const char *threat = "nil";
		const char *cat = "nil";
		char client[INET6_ADDRSTRLEN];
		char server[INET6_ADDRSTRLEN];
		char macaddr[MAC_ADDR_STR_LEN];
		inet_ntop(AF_INET6, c->ctx.src, client, sizeof(client));
		inet_ntop(AF_INET6, c->ctx.dst, server, sizeof(server));

		if (id && c->id != id) continue;
		if (proto && proto != (int)c->ctx.proto) continue;
		if (sport && htons(sport) != c->ctx.sport) continue;
		if (dport && htons(dport) != c->ctx.dport) continue;

		if (classify_flags && c->pml) {
			alp_len = pml_nameof(c->pml, c->ctx.alp, &alp);
			assert(alp_len >= 0);
			app_len = pml_nameof(c->pml, c->ctx.app, &app);
			assert(app_len >= 0);
			dev_len = pml_nameof(c->pml, c->ctx.dev, &dev);
			assert(dev_len >= 0);
			geo_len = pml_nameof(c->pml, c->ctx.geo, &geo);
			assert(geo_len >= 0);
			threat_len = pml_nameof(c->pml, c->ctx.threat, &threat);
			assert(threat_len >= 0);
			cat_len = pml_nameof(c->pml, c->ctx.cat, &cat);
			assert(cat_len >= 0);
		}

		printf(
			"{ \"id\": %u, \"smac\": \"%s\", \"proto\": %u, \"alp\": \"%.*s\", \"app\": \"%.*s\", "
			"\"dev\": \"%.*s\", \"geo\": \"%.*s\", \"threat\": \"%.*s\", \"cat\": \"%.*s\", "
			"\"src\": \"%s[%d]\", \"dst\": \"%s[%d]\", \"first_pkt\": %zu, \"src_pkts\": %zu, "
			"\"src_bytes\": %zu, \"src_scanned\": %zu, \"dst_pkts\": %zu, \"dst_bytes\": %zu, \"dst_scanned\": %zu, \"verdict\": \"%s\", \"server_name\": \"%s\" }\n",
			c->id, format_macaddr(c->ctx.device, macaddr, sizeof(macaddr)), c->ctx.proto, alp_len, alp, app_len, app,
			dev_len, dev, geo_len, geo, threat_len, threat, cat_len, cat,
			client, ntohs(c->ctx.sport), server, ntohs(c->ctx.dport), c->first_pkt,
			c->packets[0], c->bytes[0], c->scanned[0], c->packets[1], c->bytes[1], c->scanned[1],
			verdict_string(c->ctx.verdict), c->server_name ? c->server_name : ""
		);
	}
	return 0;
}

void
ct_dump()
{
	char *argv[] = { "show" };
	ct_show(1, argv);
}

struct cli ct_cli[] = {
	{
		.cmd = "ct help",
		.fun = ct_help,
	},
	{
		.cmd = "ct show",
		.fun = ct_show,
	}
};

static int
ct_init()
{
	for (int i = 0; i < CT_SIZE; i++)
		list_init(&table[i]);
	list_init(&lru);
	return register_cli(ct_cli, ARRAY_SIZE(ct_cli));
}

static void
ct_exit()
{
	ct_clear();
	unregister_cli(ct_cli, ARRAY_SIZE(ct_cli));
}

module_init(ct_init);
module_exit(ct_exit);
