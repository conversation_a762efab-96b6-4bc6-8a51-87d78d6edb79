#ifndef DEVICE_H
#define DEVICE_H

#include <stdint.h>
#include <ev.h>
#include "list.h"

struct packet;
struct device {
	struct list_head list;
	ev_timer timer;
	ev_io    io;
	int (*receive)(struct device *, unsigned *v, unsigned);
	int (*release)(struct device *, unsigned *v, unsigned);
	int (*get_packet)(struct device *, unsigned id, struct packet *);
	void (*periodic)(struct device *);
	int (*get_selectable_fd)(struct device *);
	uint64_t rx_packets;
	uint64_t rx_bytes;
	uint64_t rx_errors;
	uint64_t rx_drops;
	uint64_t ts;
};

int    register_device(struct device *);
void unregister_device(struct device *);

typedef int (*clock_gettime_t)(clockid_t id, struct timespec *tp);
extern clock_gettime_t pal_gettime;

#endif
