#ifndef CT_H
#define CT_H

#include <assert.h>

#include "flow.h"
#include "list.h"

extern uint32_t ruleset_state;
extern struct pml *ruleset_pml;

enum {
	F_FILTER   = (1 << 0),
	F_CLASSIFY = (1 << 1),
	F_CONTROL  = (1 << 2),
	F_REPORT   = (1 << 3),
	F_REVERSED = (1 << 4),
};

struct context {
	uint8_t src[16];   /* ip4 or ip6 src */
	uint8_t dst[16];   /* ip4 or ip6 dst */
	uint16_t sport;    /* src port */
	uint16_t dport;    /* dst port */
	uint8_t proto;     /* ip protocol */
	uint8_t verdict;   /* verdict (in or out param)  */
	uint16_t szone;    /* src zone */
	uint16_t dzone;    /* dst zone */
	uint16_t group;    /* group id */
	uint32_t user;     /* user id */
	uint64_t host;     /* hash of hostname */
	uint8_t device[6]; /* device id (client mac) */
	uint16_t alp;      /* application layer protocol */
	uint16_t app;      /* detected application */
	uint16_t dev;      /* device type */
	uint16_t geo;      /* geographic region */
	uint16_t threat;   /* threat id */
	uint16_t cat;      /* traffic category (always zero when app set) */
	uint16_t date;     /* must be set with pml_datetime() to use rule schedule */
	/* 4 bytes of padding if not packed. */
} __attribute__((packed));

static_assert(sizeof(struct context) == 76);
// Verify that the field offsets match what is expected by signatures
static_assert(__builtin_offsetof(struct context, src) == 0);
static_assert(__builtin_offsetof(struct context, dst) == 16);
static_assert(__builtin_offsetof(struct context, sport) == 32);
static_assert(__builtin_offsetof(struct context, dport) == 34);
static_assert(__builtin_offsetof(struct context, proto) == 36);
static_assert(__builtin_offsetof(struct context, verdict) == 37);
static_assert(__builtin_offsetof(struct context, szone) == 38);
static_assert(__builtin_offsetof(struct context, dzone) == 40);
static_assert(__builtin_offsetof(struct context, group) == 42);
static_assert(__builtin_offsetof(struct context, user) == 44);
static_assert(__builtin_offsetof(struct context, host) == 48);
static_assert(__builtin_offsetof(struct context, device) == 56);
static_assert(__builtin_offsetof(struct context, alp) == 62);
static_assert(__builtin_offsetof(struct context, app) == 64);
static_assert(__builtin_offsetof(struct context, dev) == 66);
static_assert(__builtin_offsetof(struct context, geo) == 68);
static_assert(__builtin_offsetof(struct context, threat) == 70);
static_assert(__builtin_offsetof(struct context, cat) == 72);
static_assert(__builtin_offsetof(struct context, date) == 74);

struct conn {
	struct list_head lru;
	struct list_head list;
	uint32_t id;
	uint64_t start;
	uint64_t touched;
	uint64_t first_pkt;
	uint64_t bytes[2];
	uint64_t packets[2];
	uint64_t scanned[2];
	uint32_t flags;
	uint32_t rule_state;
	struct pml *pml;
	struct flow_key key;
	struct context ctx;
	char *server_name;
};

/* ct_upsert: insert or update a connection
 *
 * Connection are presumed to client initiated (dir=0). Subsequent lookups
 * will set dir equal to 0 or 1 depending on whether the lookup key had to
 * be inverted to match. Computed hash values are symmetric regardless of
 * the direction.
 */
int ct_upsert(struct flow_key *key, uint8_t *dir, struct conn **cp, const uint64_t tick);

/* ct_expire: periodically called to remove expired connections. */
void ct_expire(const uint64_t tick);

/* ct_dump: dump table */
void ct_dump();

#endif
