#include <string.h>
#include "driver.h"

static struct list_head list = { .next = &list, .prev = &list };

int
register_driver(struct driver *d)
{
	list_insert(&list, &d->list);
	return 0;
}

void
unregister_driver(struct driver *d)
{
	list_remove(&d->list);	
}

struct driver *
lookup_driver(const char *name)
{
	struct driver *d;
	list_for_each_entry(d, &list, list) {
		if (!strcmp(d->name, name))
			return d;
	}
	return 0;
}
