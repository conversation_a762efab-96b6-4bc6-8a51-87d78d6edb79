#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <assert.h>
#include <time.h>

#include "module.h"
#include "driver.h"
#include "device.h"
#include "packet.h"

struct rand_device {
	struct device dev;
	int fd;
	unsigned short rlen;      /* next read length */
	unsigned short dlen;      /* data length */
	unsigned char data[2048];
};

static inline uint64_t
rand_gettime()
{
	struct timespec ts;
	clock_gettime(CLOCK_MONOTONIC, &ts);
	return (uint64_t)ts.tv_sec * 1000 + ts.tv_nsec / 1000000;
}

static int
rand_device_receive(struct device *dev, unsigned *vp, unsigned vs)
{
	(void)vp;
	(void)vs;
	struct rand_device *pd = container_of(dev, struct rand_device, dev);
	if (vs > 0) {
		pd->dlen = pd->rlen % sizeof(pd->data);
		if (read(pd->fd, pd->data, pd->dlen) == pd->dlen)
			memcpy(&pd->rlen, pd->data, sizeof(pd->rlen));
		vp[0] = 0;
		return 1;
	}
	return 0;
}

static int
rand_device_release(struct device *dev, unsigned *vp, unsigned vs)
{
	(void)vs;
	(void)vp;
	struct rand_device *pd = container_of(dev, struct rand_device, dev);
	pd->dlen = 0;
	assert(vs == 1 && vp[0] == 0);
	return 0;
}

static int
rand_device_packet(struct device *dev, unsigned id, struct packet *packet)
{
	(void) id;
	struct rand_device *pd = container_of(dev, struct rand_device, dev);
	packet->len = pd->dlen;
	packet->data = pd->data;
	packet->ts = dev->ts;
	return 0;
}

static void
rand_device_periodic(struct device *dev)
{
	dev->ts = rand_gettime();
}

static int
rand_device_selectable_fd(struct device *dev)
{
	struct rand_device *pd = container_of(dev, struct rand_device, dev);
	return pd->fd;
}

static struct device *
rand_device_create(const char *name)
{
	(void) name;
	struct rand_device *pd = malloc(sizeof(*pd));
	struct device *dev = &pd->dev;
	dev->receive = rand_device_receive;
	dev->release = rand_device_release;
	dev->periodic = rand_device_periodic;
	dev->get_packet = rand_device_packet;
	dev->get_selectable_fd = rand_device_selectable_fd;
	pd->fd = open("/dev/random", O_RDONLY);
	pd->dlen = 0;
	if (read(pd->fd, &pd->rlen, sizeof(pd->rlen)) == sizeof(pd->rlen))
		register_device(dev);
	return dev;
}

static void
rand_device_remove(struct device *dev)
{
	struct rand_device *pd = container_of(dev, struct rand_device, dev);
	close(pd->fd);
	unregister_device(&pd->dev);
	free(pd);
}

static struct driver rand_driver = {
	.name = "rand",
	.create = rand_device_create,
	.remove = rand_device_remove
};

static int
rand_module_init()
{
	return register_driver(&rand_driver);
}

static void
rand_module_exit()
{
	unregister_driver(&rand_driver);
}

module_init(rand_module_init);
module_exit(rand_module_exit);
