#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <net/if.h>
#include <sys/socket.h>
#include <sys/mman.h>
#include <arpa/inet.h>
#include <linux/if_packet.h>
#include <linux/if_ether.h>
#include <linux/ip.h>
#include <assert.h>
#include <time.h>

#include "module.h"
#include "driver.h"
#include "device.h"
#include "packet.h"

struct block_desc {
	uint32_t version;
	uint32_t offset_to_priv;
	struct tpacket_hdr_v1 h1;
};
	
struct mmap_device {
	struct device dev;
	int fd;
	struct iovec *rd;
	uint8_t *map;
	struct tpacket_req3 req;
	unsigned block_num;
};

static inline uint64_t
mmap_gettime()
{
	struct timespec ts;
	clock_gettime(CLOCK_MONOTONIC, &ts);
	return (uint64_t)ts.tv_sec * 1000 + ts.tv_nsec / 1000000;
}

static int
read_block(struct block_desc *pbd, unsigned block_num, unsigned *vp)
{
	unsigned i, vi = 0;
	struct tpacket3_hdr *ppd = (struct tpacket3_hdr *)((uint8_t *) pbd +
			pbd->h1.offset_to_first_pkt);

	for (i = 0; i < pbd->h1.num_pkts; i++) {
		vp[vi++] = (((uint8_t *)ppd - (uint8_t *)pbd) << 6) | (block_num & 0x3f);
		ppd = (struct tpacket3_hdr *) ((uint8_t *) ppd + 
				ppd->tp_next_offset);
	}
	return i;
}

static int
mmap_device_receive(struct device *dev, unsigned *vp, unsigned vs)
{
	struct block_desc *pbd;
	struct mmap_device *pd = container_of(dev, struct mmap_device, dev);
	unsigned tr = 0, block_num = pd->block_num;
	pbd = (struct block_desc *) pd->rd[block_num].iov_base;
	assert(pbd->h1.num_pkts <= vs + 1);
	assert(pbd->h1.num_pkts >= 1);
	while (pbd->h1.num_pkts <= vs) {
		unsigned nr = read_block(pbd, block_num, vp);
		vp += nr; vs -= nr; tr += nr;
		block_num = (block_num + 1) & 63;
		pbd = (struct block_desc *) pd->rd[block_num].iov_base;
		if ((pbd->h1.block_status & TP_STATUS_USER) == 0)
			break;
	}
	pd->block_num = block_num;
	return tr;
}

static int
mmap_device_release(struct device *dev, unsigned *vp, unsigned vs)
{
	struct mmap_device *pd = container_of(dev, struct mmap_device, dev);
	struct block_desc *last = 0;
	assert(vs);
	for (unsigned i = 0; i < vs; i++) {
		unsigned id = vp[i];
		unsigned block_num = id & 0x3f;
		//unsigned offset = id >> 6;
		struct block_desc *pbd = pd->rd[block_num].iov_base;
		if (last == 0) {
			last = pbd;
		} else if (last != pbd) {
			last->h1.block_status = TP_STATUS_KERNEL;
			last = pbd;
		}
	}
	last->h1.block_status = TP_STATUS_KERNEL;
	return 0;
}

static int
mmap_device_packet(struct device *dev, unsigned id, struct packet *packet)
{
	struct mmap_device *pd = container_of(dev, struct mmap_device, dev);
	unsigned block_num = id & 0x3f;
	unsigned offset = id >> 6;
	struct block_desc *pbd = pd->rd[block_num].iov_base;
	struct tpacket3_hdr *ppd = (struct tpacket3_hdr *)((uint8_t *) pbd + offset);
	packet->len = ppd->tp_snaplen;
	packet->data = (uint8_t *) ppd + ppd->tp_mac;
	packet->ts = dev->ts;
	return 0;
}

static void
mmap_device_periodic(struct device *dev)
{
	dev->ts = mmap_gettime();
}

static int
mmap_device_selectable_fd(struct device *dev)
{
	struct mmap_device *pd = container_of(dev, struct mmap_device, dev);
	return pd->fd;
}

#define BLOCK_SIZE 131072
#define FRAME_SIZE 2048

static struct device *
mmap_device_create(const char *name)
{
	printf("mmap_device_create: %s\n", name);
	int err, fd, v = TPACKET_V3;
	uint8_t *map;
	struct iovec *rd;
	struct mmap_device *pd;
	struct device *dev;
	struct tpacket_req3 *req;
	struct sockaddr_ll ll;
	unsigned i, block_size = BLOCK_SIZE, frame_size = FRAME_SIZE, block_nr = 64;

	fd = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));
	if (fd < 0) {
		return NULL;
	}
	err = setsockopt(fd, SOL_PACKET, PACKET_VERSION, &v, sizeof(v));
	if (err < 0) {
		close(fd);
		return NULL;
	}
	pd = malloc(sizeof(*pd));
	if (!pd) {
		close(fd);
		return NULL;
	}
	dev = &pd->dev;
	req = &pd->req;

	memset(req, 0, sizeof(*req));
	req->tp_block_size = block_size;
	req->tp_frame_size = frame_size;
	req->tp_block_nr = block_nr;
	req->tp_frame_nr = (block_size * block_nr) / frame_size;
	req->tp_retire_blk_tov = 60;
	//req->tp_feature_req_word = TP_FT_REQ_FILL_RXHASH;

	err = setsockopt(fd, SOL_PACKET, PACKET_RX_RING, req, sizeof(*req));
	if (err < 0) {
		close(fd);
		free(pd);
		exit(1);
	}

	// mmap 
	map = mmap(NULL, req->tp_block_size * req->tp_block_nr,
			PROT_READ | PROT_WRITE, MAP_SHARED | MAP_LOCKED, fd, 0);
	if (map == MAP_FAILED) {
		perror("mmap");
		close(fd);
		free(pd);
		return NULL;
	}

	// setup iovec
	rd = malloc(req->tp_block_nr * sizeof(*rd));
	for (i = 0; i < req->tp_block_nr; i++) {
		rd[i].iov_base = map + (i * req->tp_block_size);
		rd[i].iov_len = req->tp_block_size;
	}

	memset(&ll, 0, sizeof(ll));
	ll.sll_family = PF_PACKET;
	ll.sll_protocol = htons(ETH_P_ALL);
	ll.sll_ifindex = if_nametoindex(name);
	ll.sll_hatype = 0;
	ll.sll_pkttype = 0;
	ll.sll_halen = 0;

	err = bind(fd, (struct sockaddr *) &ll, sizeof(ll));
	if (err < 0) {
		free(rd);
		munmap(map, req->tp_block_size * req->tp_block_nr);
		close(fd);
		free(pd);
	}

	pd->fd = fd;
	pd->rd = rd;
	pd->block_num = 0;

	dev->receive = mmap_device_receive;
	dev->release = mmap_device_release;
	dev->periodic = mmap_device_periodic;
	dev->get_packet = mmap_device_packet;
	dev->get_selectable_fd = mmap_device_selectable_fd;

	register_device(dev);
	return dev;
}

static void
mmap_device_remove(struct device *dev)
{
	printf("mmap_device_remove:\n");
	struct mmap_device *pd = container_of(dev, struct mmap_device, dev);
	munmap(pd->map, pd->req.tp_block_size * pd->req.tp_block_nr);
	free(pd->rd);
	close(pd->fd);
	unregister_device(&pd->dev);
	free(pd);
}

static struct driver mmap_driver = {
	.name = "mmap",
	.create = mmap_device_create,
	.remove = mmap_device_remove
};

static int
mmap_module_init()
{
	return register_driver(&mmap_driver);
}

static void
mmap_module_exit()
{
	unregister_driver(&mmap_driver);
}

module_init(mmap_module_init);
module_exit(mmap_module_exit);
