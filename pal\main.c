#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <readline/readline.h>
#include <readline/history.h>

#include "device.h"
#include "driver.h"
#include "config.h"
#include "cli.h"
#include "ct.h"

static struct device *
start(const char *s)
{
	struct driver *d;
	char *p, dst[strlen(s) + 1];
	strcpy(dst, s);
	p = strchr(dst, ':');
	if (!p) return NULL;
	*p++ = '\0';
	if (!(d = lookup_driver(dst))) return NULL;
	return d->create(p);
}

static int
stop(const char *s, struct device *dev)
{
	struct driver *d;
	char *p, dst[strlen(s) + 1];
	strcpy(dst, s);
	p = strchr(dst, ':');
	if (!p) return -1;
	*p++ = '\0';
	if (!(d = lookup_driver(dst))) return -1;
	d->remove(dev);
	return 0;
}

static void
readline_cb(char *line)
{
	if (line) {
		if (*line) {
			add_history(line);
		}
		cli_execute(line);
		free(line);
	}
}

static void
stdin_cb(EV_P_ ev_io *w, int revents)
{
	(void) w;
	if (revents & EV_READ)
		rl_callback_read_char();
}

int
main(int argc, char *argv[])
{
	const char *device = 0;
	struct device *dev;
	int opt, interactive = 0;
	while ((opt = getopt(argc, argv, "d:hiv")) != -1) {
		switch (opt) {
		case 'd':
			device = optarg;
			break;
		case 'i':
			interactive = 1;
			break;
		case 'v':
			fprintf(stdout, "Version: %s", PAL_VERSION);
			exit(EXIT_SUCCESS);
		case 'h':
		default:
			fprintf(stderr, "Usage: %s [-d device]\n", argv[0]);
			exit(EXIT_FAILURE);
		}
	}

	if (!device || !(dev = start(device))) {
		fprintf(stderr, "Failed to start %s\n", device);
		exit(EXIT_FAILURE);
	}

	struct ev_loop *loop = EV_DEFAULT;

	if (!interactive) {
		ev_run(loop, 0);
		ct_dump();
		stop(device, dev);
		ev_loop_destroy(loop);
		return 0;
	}

	/* readline */
	fcntl(fileno(stdin), F_SETFL, O_NONBLOCK);
	rl_callback_handler_install(">> ", (rl_vcpfunc_t *) &readline_cb);

	ev_io stdin_watcher;
	ev_io_init(&stdin_watcher, stdin_cb, fileno(stdin), EV_READ);
	ev_io_start(loop, &stdin_watcher);

	ev_run(loop, 0);

	rl_callback_handler_remove();
	return 0;
}
