#include <stdio.h>
#include <malloc.h>
#include <time.h>

#include <pml/pml.h>

#include "device.h"
#include "module.h"
#include "classify.h"
#include "packet.h"
#include "control.h"
#include "ct.h"

static struct pml *master;
uint32_t classify_flags = 0;

void
classify(struct packet *packet)
{
	struct conn *conn = packet->conn;
	int state = pml_scan(conn->pml, packet->data, packet->len, packet->dir, &conn->ctx);
	conn->scanned[packet->dir]++;
	if (!state) {
		conn->flags &= ~F_CLASSIFY;
		conn->flags |= control_flags;
	}
}

void
classify_start(struct conn *conn)
{
	if (classify_flags && !pml_clone(&conn->pml, master)) {
		pml_main(conn->pml, &conn->ctx);
		conn->flags |= F_CLASSIFY;
	}
}

void
classify_stop(struct conn *conn)
{
	if (conn->pml)
		pml_exit(conn->pml);
	free(conn->server_name);
}

static void
watch_server_name(void *ctx, int key, int type, uint32_t length, const void *value)
{
	struct conn *conn = container_of(ctx, struct conn, ctx);
	(void)key;
	if (type == WATCH_STR) {
		char *sn = realloc(conn->server_name, length + 1);
		if (!sn) return;
		memcpy(sn, value, length);
		/* null terminate the string */
		sn[length] = 0;
		conn->server_name = sn;
	}
}

int
classify_init()
{
	pml_init();
	if (pml_load(&master, "signatures.bin") < 0) {
		fprintf(stderr, "pml_load(classify) failed\n");
		return -1;
	}
	pml_watchpoint(master, "server_name", watch_server_name);
	classify_flags = F_CLASSIFY;
	return 0;
}

void
classify_exit()
{
	if (master)
		pml_exit(master);
	classify_flags = 0;
}

module_init(classify_init);
module_exit(classify_exit);
