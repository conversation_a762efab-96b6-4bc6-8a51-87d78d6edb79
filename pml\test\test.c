#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

#define OPTION_TEST
#ifdef OPTION_TEST

static struct pml_find_stats {
	uint32_t perfect_matching;
	uint32_t perfect_available;
	uint32_t perfect_displaced;
	uint32_t value_expired;
	uint32_t probe_found;
	uint32_t probe_sentinel;
	uint32_t probe_relocation;
	uint32_t probe_tombstone;
	uint32_t probe_count;
	uint32_t probe_loops;
	uint32_t value_deleted;
	uint32_t left_adjacent;
} stats;

static void
zero_stats()
{
	memset(&stats, 0, sizeof(stats));
}

static void
dump_stats(const char *test)
{
	printf("{\"test\":\"%s\",", test);
	printf("\"stat\": {");
	printf("\"perfect_matching\": %u,", stats.perfect_matching);
	printf("\"perfect_available\": %u,", stats.perfect_available);
	printf("\"perfect_displaced\": %u,", stats.perfect_displaced);
	printf("\"value_expired\": %u,", stats.value_expired);
	printf("\"probe_found\": %u,", stats.probe_found);
	printf("\"probe_sentinel\": %u,", stats.probe_sentinel);
	printf("\"probe_relocation\": %u,", stats.probe_relocation);
	printf("\"probe_tombstone\": %u,", stats.probe_tombstone);
	printf("\"probe_count\": %u,", stats.probe_count);
	printf("\"probe_loops\": %u,", stats.probe_loops);
	printf("\"left_adjacent\": %u,", stats.left_adjacent);
	printf("\"value_deleted\": %u,", stats.value_deleted);
	printf("\"probe_average\": %.2f", stats.probe_count ? stats.probe_loops / (float)stats.probe_count : 0.0);
	printf("}}");
	zero_stats();
}

#else
#define dump_stats(s) (void) s
#endif
#include "../src/pml.c"

static void
dump_obj(struct pml_obj *obj)
{
	char *buf = 0;
	int len = pml_serialize(obj, &buf);
	printf("%s", buf);
	free(buf);
}

static struct pml_obj
pml_buf_sel(struct pml_obj *from, uint16_t offset, uint16_t length)
{
	assert(from->type == PML_BUF);
	struct pml_obj v = { .type = from->type };
	v.v.b = from->v.b;
	v.offset = from->offset + offset;
	v.length = length;
	v.v.b->ref++;
	return v;
}

int
pml_test_map_perfect_keys()
{
	struct pml_map *m = pml_map(8);
	struct pml_obj key, val, def, *res;

	key = pml_int(13);
	val = pml_str_dup("thirteen");
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	key = pml_int(14);
	val = pml_str_dup("fourteen");
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	key = pml_int(5);
	val = pml_str_dup("five");
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	key = pml_int(6);
	val = pml_str_dup("six");
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	struct pml_obj map = { .type = PML_MAP, .v.m = m };
	dump_obj(&map);
	pml_obj_release(&map);
	return 0;
}

int
pml_test_map_string_keys()
{
	struct pml_map *m = pml_map(8);
	struct pml_obj key, val, *res;

	key = pml_str_dup("thirteen");
	val = pml_int(13);
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	key = pml_str_dup("fourteen");
	val = pml_int(14);
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	key = pml_str_dup("five");
	val = pml_int(5);
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	key = pml_str_dup("six");
	val = pml_int(6);
	pml_map_set(m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);

	struct pml_obj map = { .type = PML_MAP, .v.m = m };
	dump_obj(&map);
	pml_obj_release(&map);
	return 0;
}


int
pml_test_map_recursive_storage()
{
	/* Without weak references or more sophisticated garbage collection,
	 * recursive storage will be difficult to implement in a robust way.
	 * Thus this test covers basic functionality, but is careful to clear
	 * the map before dropping its own reference to prevent leaks.
	 */
	struct pml_map *m = pml_map(8);
	struct pml_obj key, val, *res;

	key = pml_str_dup("abc");
	val = (struct pml_obj){ .type = PML_MAP, .v.m = m };

	/* set */
	pml_map_set(m, &key, &val);

	/* get */
	res = pml_map_get(m, &key, PML_INT);

	/* release key */
	pml_obj_release(&key);

	/* display map */
	struct pml_obj map = { .type = PML_MAP, .v.m = m };
	dump_obj(&map);

	/* clear first (calls release on each entry pair) */
	pml_map_clear(m);
	/* release map */
	pml_obj_release(&map);
	return 0;
}

int
pml_test_map_and_slice()
{
	struct pml_map *m = pml_map(8);
	struct pml_obj k1 = pml_int(99);
	struct pml_obj v1 = pml_str_dup("two");
	struct pml_obj k2 = pml_int(3);
	struct pml_obj v2 = pml_buf_sel(&v1, 1, 2);

	pml_map_set(m, &k1, &v1);
	pml_obj_release(&k1);
	pml_obj_release(&v1);

	pml_map_set(m, &k2, &v2);
	pml_obj_release(&k2);
	pml_obj_release(&v2);

	struct pml_obj k3 = pml_int(3);
	pml_map_del(m, &k3);

	struct pml_obj k4 = pml_int(99);
	struct pml_obj *v4 = pml_map_get(m, &k4, PML_INT);

	struct pml_obj *v = v4;
	assert(v->v.b->ref == 1);
	assert(!strncmp(v->v.b->data, "two", v->v.b->size));
	PML_TRACE("%.*s (ref=%u)\n", v->v.b->size, v->v.b->data, v->v.b->ref);

	pml_map_release(m);
	return 0;
}

int
pml_test_map_replication_handler(const char *buf, uint32_t len)
{
	printf("%s", buf);
	return 0;
}

int
pml_test_map_replication()
{
	struct pml_map *m = pml_map(8);
	/* The map index is dual purpose:
	 *
	 * 1) For a root node, the index holds the maps replication id. This id is used
	 *    whenever any modification is made to this map or any of its children.
	 *
	 * 2) For a child node, the index holds the index of the kvp where this map is
	 *    located. This is necessary to encode the intermediate path.
	 *
	 * A map node is a child if it has a parent.
	 * A map node is a root node when its parent member points to itself.
	 */

	/* Set replication id on root map */
	m->index = 1;
	m->parent = NULL;

	/* Listen for events */
	pml_listen(pml_test_map_replication_handler);

	/* Ex 1: Simple map with string value */
	{
		struct pml_obj k = pml_int(3);
		struct pml_obj v = pml_str_dup("three");
		pml_map_set(m, &k, &v);
		pml_replicate(m, &k, &v);
		pml_obj_release(&k);
		pml_obj_release(&v);
	}
	/* Ex 2: Simple map with nested map value */
	{
		struct pml_obj k = pml_int(4);
		struct pml_obj v = (struct pml_obj){.type = PML_MAP, .v.m = pml_map(8)};
		pml_map_set(m, &k, &v);
		pml_replicate(m, &k, &v);
		pml_obj_release(&k);
		pml_obj_release(&v);
	}
	/* Ex 3: Nested map from Ex 2 updated with integer value mapped from key 99 */
	{
		struct pml_obj k = pml_int(4);
		struct pml_obj v = pml_int(42);
		struct pml_obj *r = pml_map_get(m, &k, PML_MAP);

		k = pml_int(99);
		pml_map_set(r->v.m, &k, &v);
		pml_replicate(r->v.m, &k, &v);
		pml_obj_release(&k);
		pml_obj_release(&v);
	}
	{
		int i;
		struct pml_map *rep = pml_map(8);
		struct pml_map *path = pml_map(8);
		struct pml_obj repobj = {.type = PML_MAP, .v.m = rep};
		struct pml_obj k = pml_int(0);
		struct pml_obj v = pml_int(43);
		int64_t keys[] = {1, 4, 99};
		for (i = 0; i < 3; i++) {
			struct pml_obj k = pml_int(i);
			struct pml_obj v = pml_int(keys[i]);
			pml_map_set(path, &k, &v);
		}
		pml_map_path_set(rep, path, &v);
		pml_map_release(path);
		pml_map_release(rep);
	}

	pml_map_release(m);
	return 0;
}

struct pml_obj
pml_int_with_expiry(int64_t value, uint32_t expiry)
{
	struct pml_obj val = pml_int(value);
	val.expires = T(expiry);
	return val;
}

int
pml_test_map_performance()
{
	struct pml_map *m;

	uint32_t i, d = 0, j = 0, it;

	srandom(time(NULL));

	{
		m = pml_map(8);

		/* Try to add 100k random keys */
		while (m->size < 100000) {
			struct pml_obj key = pml_int((uint32_t)random());
			pml_map_get(m, &key, PML_INT);
		}

		/* Walk the map to test reclaimation */
		struct pml_obj *k, *v;
		pml_map_for_each_entry(m, k, v) {
			pml_map_del(m, k);
		}

		pml_map_release(m);
		dump_stats("random");
	}

	{
		struct pml_obj key;
		m = pml_map(8);
		/* insert 3 */
	      	key = pml_int(3); pml_map_get(m, &key, PML_INT);
		/* insert 11 (which collides with 3 but is not a perfect hash
		 * so it should probe */
		key = pml_int(11); pml_map_get(m, &key, PML_INT);
		/* delete 3 */
		key = pml_int(3); pml_map_del(m, &key);
		/* insert 4 (which collides with 11's index and should displace
		 * it so 4 is perfectly inserted) */
		key = pml_int(4); pml_map_get(m, &key, PML_INT);
		/* check */
		dump_stats("collide");
		pml_map_release(m);
	}

	{
		struct pml_obj key;
		m = pml_map(8);

		key = pml_int(0); pml_map_get(m, &key, PML_INT); /* [ 0, EMPTY, EMPTY ] */
		key = pml_int(1); pml_map_get(m, &key, PML_INT); /* [ 0, 1, EMPTY ] */
		key = pml_int(2); pml_map_get(m, &key, PML_INT); /* [ 0, 1, 2 ] */

		key = pml_int(0); pml_map_del(m, &key); /* [ DELETED, 1, 2 ] */
		key = pml_int(1); pml_map_del(m, &key); /* [ DELETED, DELETED, 2] */
		key = pml_int(2); pml_map_del(m, &key); /* [ EMPTY, EMPTY, EMPTY ] */

		dump_stats("reclaim");
		pml_map_release(m);
	}
	
	{
		struct pml_obj key, val;
		m = pml_map(8);

		key = pml_int(0), val = pml_int_with_expiry(0, 1);
		pml_map_set(m, &key, &val);

		sleep(2);

		key = pml_int(0), val = pml_int_with_expiry(0, 2);
		pml_map_set(m, &key, &val);

		dump_stats("expiry");
		pml_map_release(m);
	}

	return 0;
}

int
pml_test_deserialize()
{
	int err;
	struct pml_obj obj;
	const char *input = "{\"99\":{\"0\":8,\"1\":9,\"2\":{\"0\":\"a string\"}}}";
	char *output;
	
	// deserialize string into object
	err = pml_deserialize(input, strlen(input), &obj); assert(!err);

	// serialize object back into string
	err = pml_serialize(&obj, &output);

	printf("%s", input);
	printf("%s", output);

	pml_obj_release(&obj);
	free(output);
	return 0;
}

int main(void)
{
//	pml_test_map_and_slice();
//	pml_test_map_perfect_keys();
//	pml_test_map_string_keys();
//	pml_test_map_recursive_storage();
	pml_test_map_replication();
//	pml_test_map_performance();
	pml_test_deserialize();
	return 0;
}
