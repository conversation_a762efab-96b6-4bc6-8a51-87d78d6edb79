#ifndef LIST_H
#define LIST_H

#include <stdbool.h>

struct list_head {
	struct list_head *prev;
	struct list_head *next;
};

static inline void
list_init(struct list_head *list)
{
	list->next = list;
	list->prev = list;
}

static inline void
list_insert(struct list_head *list, struct list_head *item)
{
	item->next = list;
	item->prev = list->prev;
	item->next->prev = item;
	item->prev->next = item;
}

static inline void
list_remove(struct list_head *item)
{
	item->next->prev = item->prev;
	item->prev->next = item->next;
}

static inline bool
list_empty(struct list_head *list)
{
	return list->next == list;
}

#define container_of(ptr, type, member) \
	(type *)((char *)ptr - __builtin_offsetof(type, member))

#define list_for_each_entry(ptr, head, member) \
	for (ptr = container_of((head)->next, typeof(*ptr), member); \
			&ptr->member != (head); \
			ptr = container_of(ptr->member.next, typeof(*ptr), member))

#define list_for_each_entry_safe(ptr, tmp, head, member) \
	for (ptr = container_of((head)->next, typeof(*ptr), member), \
		tmp = container_of(ptr->member.next, typeof(*ptr), member); \
			&ptr->member != (head); \
			ptr = tmp, tmp = container_of(ptr->member.next, typeof(*ptr), member))

#define list_for_each_entry_reverse(ptr, head, member) \
	for (ptr = container_of((head)->prev, typeof(*ptr), member); \
			&ptr->member != (head); \
			ptr = container_of(ptr->member.prev, typeof(*ptr), member))


#endif
