#ifndef CONTROL_H
#define CONTROL_H

#include <stdint.h>

struct packet;
void control(struct packet *);

extern uint32_t control_flags;

/* Defined to match src/nsa_policy.h:nsa_policy_action_t */
enum verdict {
	VERDICT_NONE = 0,
	VERDICT_ALLOW = 1,
	VERDICT_DENY = 2,
	VERDICT_DROP = 3
#if 0
	/* Unused */
	VERDICT_REDIRECT = 4,
	VERDICT_QOS_LIMIT = 5,
	VERDICT_LOG_ONLY = 6,
	VERDICT_DECRYPT = 7,
	VERDICT_SANDBOX = 8
#endif
};

#endif
