#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <assert.h>
#include <errno.h>

#include "../include/pml.h"

struct context {
	uint8_t src[16];   /* ip4 or ip6 src */
	uint8_t dst[16];   /* ip4 or ip6 dst */
	uint16_t sport;    /* src port */
	uint16_t dport;    /* dst port */
	uint8_t proto;     /* ip protocol */
	uint8_t verdict;   /* verdict (in or out param)  */
	uint16_t szone;    /* src zone */
	uint16_t dzone;    /* dst zone */
	uint16_t group;    /* group id */
	uint32_t user;     /* user id */
	uint64_t host;     /* hash of hostname */
	uint8_t device[6]; /* device id (client mac) */
	uint16_t alp;      /* application layer protocol */
	uint16_t app;      /* detected application */
	uint16_t dev;      /* device type */
	uint16_t geo;      /* geographic region */
	uint16_t threat;   /* threat id */
	uint16_t cat;      /* traffic category (always zero when app set) */
	uint16_t date;     /* must be set with pml_datetime() to use rule schedule */
	/* 4 bytes of padding if not packed. */
} __attribute__((packed));

static_assert(sizeof(struct context) == 76);
// Verify that the field offsets match what is expected by signatures
static_assert(__builtin_offsetof(struct context, src) == 0);
static_assert(__builtin_offsetof(struct context, dst) == 16);
static_assert(__builtin_offsetof(struct context, sport) == 32);
static_assert(__builtin_offsetof(struct context, dport) == 34);
static_assert(__builtin_offsetof(struct context, proto) == 36);
static_assert(__builtin_offsetof(struct context, verdict) == 37);
static_assert(__builtin_offsetof(struct context, szone) == 38);
static_assert(__builtin_offsetof(struct context, dzone) == 40);
static_assert(__builtin_offsetof(struct context, group) == 42);
static_assert(__builtin_offsetof(struct context, user) == 44);
static_assert(__builtin_offsetof(struct context, host) == 48);
static_assert(__builtin_offsetof(struct context, device) == 56);
static_assert(__builtin_offsetof(struct context, alp) == 62);
static_assert(__builtin_offsetof(struct context, app) == 64);
static_assert(__builtin_offsetof(struct context, dev) == 66);
static_assert(__builtin_offsetof(struct context, geo) == 68);
static_assert(__builtin_offsetof(struct context, threat) == 70);
static_assert(__builtin_offsetof(struct context, cat) == 72);
static_assert(__builtin_offsetof(struct context, date) == 74);

static void
dump_results(struct pml *pml, struct context *ctx)
{
	int len;
	const char *name;
	printf("{");
	if (ctx->alp) {
		len = pml_nameof(pml, ctx->alp, &name);
		printf("\"alp\":\"%.*s\"", len, name);
	}
	if (ctx->app) {
		len = pml_nameof(pml, ctx->app, &name);
		printf("%s\"app\":\"%.*s\"", ctx->alp ? "," : "", len, name);
	}
	printf("}\n");
}

static void
dump_strings(struct pml *pml)
{
	uint32_t i;
	const char *ch = "";

	printf("[");
	for (i = 0;; i++) {
		const char *name;
		int len;
		if ((len = pml_nameof(pml, i, &name)) == -ERANGE)
			break;
		if (len <= 0)
			continue;
		printf("%s{\"name\":\"%.*s\",\"id\":%d}", i ? "," : "", len, name, i);
	}
	printf("]\n");
}

static void
dump_stats(struct pml *pml, struct pml_stats *stats)
{
	pml_stats(pml, stats);
	printf("{\"syncs\": %zu, \"scans\": %zu, \"evals\": %zu, \"events\": %zu, \"subscans\": %zu, \"yields\": %zu}\n",
			stats->syncs,
			stats->scans,
			stats->evals,
			stats->events,
			stats->subscans,
			stats->yields
	);
}

static int
listener_cb(const char *data, uint32_t len)
{
	printf("listener_cb: %.*s\n", len, data);
	return 0;
}

static char *watch;
static void
watch_cb(void *ctx, int key, int type, uint32_t length, const void *value)
{
	switch (type) {
	case WATCH_INT: {
		int64_t dst;
		memcpy(&dst, value, length);
		printf("%s (%d): %zd\n", watch, key, dst);
		break;
	}
	case WATCH_STR:
	case WATCH_OBJ:
		printf("%s (%d): %.*s\n", watch, key, length, (const char *)value);
		break;
	}
}

static void
usage()
{
	fprintf(stderr,
		"usage: pmlt [OPTIONS] <program.bin> <string>...\n"
		"\n"
		"OPTIONS:\n"
		"  -b <bsz>    specify the maximum buffer size (default=100)\n"
		"  -c          use a pml clone to perform the scan\n"
		"  -l          attach a listener for command replication\n"
		"  -p <num>    override ctx proto (default=1)\n"
		"  -r          dump results\n"
		"  -s          dump strings\n"
		"  -S          dump runtime statistics\n"
		"  -w          watch a named memory region\n"
		"\n"
		);
}

int
main(int argc, char *argv[])
{
	int ch;
	int bsz = 100, c = 0, l = 0, r = 0, s = 0, S = 0;
	char *bin;
	struct pml *pml, *clone;
	struct pml_stats stats = {0};
	int ret, state = 0;
	struct context ctx = {0};

	/* The application needs to call pml_init() once. This may be removed in the future. */
	pml_init();

	/* Parse options */
	while ((ch = getopt(argc, argv, "b:clp:rsSw:")) != -1) {
		switch (ch) {
			case 'b':
				bsz = atoi(optarg);
				break;
			case 'c':
				c = 1;
				break;
			case 'l':
				l = 1;
				break;
			case 'p':
				ctx.proto = atoi(optarg);
				break;
			case 'r':
				r = 1;
				break;
			case 's':
				s = 1;
				break;
			case 'S':
				S++;
				break;
			case 'w':
				watch = optarg;
				break;
			case '?':
				usage();
				exit(0);
		}
	}

	if (!(optind < argc))
		usage(), exit(0);

	bin = argv[optind];

	/* When option -l is specified, the listener will be called back with operations
	 * that are candidates for replication. Replication can be used to implement sharing
	 * between threads or program instances when combined with pml_publish().
	 */
	if (l)
		pml_listen(listener_cb);

	/* Load the program from disk and initialize the main pml object */
	ret = pml_load(&pml, bin);
	if (ret) {
		fprintf(stderr, "pml_load(%s) failed (%s)\n", argv[optind], strerror(-ret));
		exit(0);
	}

	if (watch) {
		ret = pml_watchpoint(pml, watch, watch_cb);
		if (ret < 0) {
			fprintf(stderr, "pml_watchpoint(%s) failed (%s)\n", watch, strerror(-ret));
			exit(0);
		}
	}

	/* When option -s is specified, strings in the loaded program are dumped to stdout */
	if (s) {
		dump_strings(pml);
	}

	/* There is data to scan */
	if (++optind < argc || !isatty(STDIN_FILENO)) {

		/* To exercize the API, -c can be used to clone the master. The clone gets a copy
		 * of the state of the master at the time of the clone request. Additionally, since
		 * maps are pass by reference, clones automatically share maps with each other. */
		clone = pml;
		if (c)
			pml_clone(&clone, pml);

		/* A pml compiled program may have a main defined. If it does not, the compiler will
		 * insert an empty main. The return type from main must be int, but its value is application
		 * defined. If the programmer does not include a main, the default value is zero. */
		ret = pml_main(clone, &ctx);

		/* Process argv */
		if (optind < argc) {
			#ifndef MIN
			#define MIN(x, y) ((x) < (y) ? (x) : (y))
			#endif
			do {
				const uint8_t *data = (const uint8_t *)argv[optind];
				int remaining = strlen(argv[optind]);
				int datalen = MIN(bsz, remaining);
				while (remaining) {
					state = pml_scan(clone, data, datalen, 0, &ctx);
					if (S > 1)
						dump_stats(pml, &stats);
					if (!state) break;
					data += datalen;
					remaining -= MIN(remaining, datalen);
				}
			} while (state && ++optind < argc);
		/* Process stdin */
		} else {
			char buffer[bsz];
			size_t n;
			while (n = fread(buffer, 1, sizeof(buffer), stdin)) {
				state = pml_scan(clone, buffer, n, 0, &ctx);
				if (S > 1)
					dump_stats(pml, &stats);
				if (!state) break;
			}
		}

		/* Clones hold a reference so they must have pml_exit() called on them when they are
		 * no longer required. */
		if (c)
			pml_exit(clone);
	}

	/* Display results in ctx */
	if (r)
		dump_results(pml, &ctx);

	if (S)
		dump_stats(pml, &stats);

	/* Exit the master - this will release program completely */
	pml_exit(pml);
	return ret;
}
