#ifndef IPADDR_H
#define IPADDR_H

#include <arpa/inet.h>

union ipaddr {
	uint16_t addr16[8];
	uint32_t addr32[4];
	uint64_t addr64[2];
};

/* ipaddr: ip4 addresses are stored as if they are mapped in ip6.
 */
static inline void
ipaddr(union ipaddr *dst, void *src, int family)
{
	switch (family) {
	case AF_INET:
		dst->addr64[0] = 0;
		dst->addr64[1] = 0;
		dst->addr16[5] = 0xffff;
		memcpy(&dst->addr32[3], src, 4);
		break;
	case AF_INET6:
		memcpy(&dst->addr64[0], src, 16);
		break;
	}
}

#endif
