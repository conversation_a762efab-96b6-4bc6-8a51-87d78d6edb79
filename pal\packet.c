#include <stdio.h>
#include <ctype.h>

#include <linux/if_ether.h>
#include <linux/ip.h>
#include <linux/tcp.h>
#include <linux/udp.h>
#include <arpa/inet.h>

#include "packet.h"
#include "flow.h"

void
packet_dump(struct packet *p)
{
	unsigned short i;
	for (i = 0; i < p->len; i++) {
		if (i % 16 == 0) {
			if (i) {
				printf("  ");
				for (int j = i - 16; j < i; j++)
					printf("%c", isalnum(p->data[j]) ? p->data[j] : '.');
			}
			printf("\n%04d: ", i);
		}
		printf(" %02x", p->data[i]);
	}

	int j = 16 - (i % 16);
	if (j) {
		for (int i = j; i; i--) printf("   ");
		printf("  ");
		for (j = i - (16 - j); j < i; j++)
			printf("%c", isalnum(p->data[j]) ? p->data[j] : '.');
	}
	printf("\n");
}

bool
packet_parse(struct packet *p, struct flow_key *key)
{
	struct ethhdr *eh;
	struct iphdr *iph;
	struct tcphdr *tcp;
	struct udphdr *udp;
	uint8_t ipl;

	eh = (struct ethhdr *)p->data;
	if (eh->h_proto != ntohs(ETH_P_IP))
		return false;

	p->data += sizeof(*eh);
	p->len  -= sizeof(*eh);
	iph = (struct iphdr *)(p->data);
	ipl = iph->ihl * 4;

	p->len = ntohs(iph->tot_len);

	if (iph->frag_off & (htobe16(0x2000 | 0x1fff)))
		return false;

	key->proto = iph->protocol;
	ipaddr(&key->saddr, &iph->saddr, AF_INET);
	ipaddr(&key->daddr, &iph->daddr, AF_INET);

	switch (iph->protocol) {
	case IPPROTO_TCP:
		tcp = (struct tcphdr *)((uint8_t *)iph + ipl);
		key->sport = tcp->source;
		key->dport = tcp->dest;
		p->data = (uint8_t *)tcp + (tcp->doff * 4);
		p->len -= (ipl + (tcp->doff * 4));
		return true;
	case IPPROTO_UDP:
		udp = (struct udphdr  *)((uint8_t *)iph + ipl);
		key->sport = udp->source;
		key->dport = udp->dest;
		p->data = (uint8_t *)udp + sizeof(*udp);
		p->len -= (ipl + sizeof(*udp));
		return true;
	default:
		p->data += p->len;
		p->len = 0;
	}
	return false;
}
