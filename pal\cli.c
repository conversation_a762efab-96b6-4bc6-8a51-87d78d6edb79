#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <assert.h>

#include "cli.h"
#include "list.h"

struct node {
	struct node *parent;
	struct list_head list;
	struct list_head children;
	char name[32];
	int (*fun)(int argc, char *argv[]);
};

#define CLI_NARGS 16
static struct list_head cli = { .prev = &cli, .next = &cli };

static int
str2argv(char *str, char **argv)
{
	char *tok, *ctx;
	int argc = 0;
	tok = strtok_r(str, " \t\n", &ctx);
	while (tok && argc < CLI_NARGS) {
		argv[argc++] = tok;
		tok = strtok_r(NULL, " ", &ctx);
	}
	return argc;
}

static int
cli_register(const char *cmd, int (*fun)(int argc, char *argv[]))
{
	char *line = strdup(cmd);
	char *argv[CLI_NARGS];
	int argc = str2argv(line, argv), index = 0;
	struct list_head *list = &cli;
	struct node *item, *found = 0, *parent = 0;

	while (index < argc) {
		found = 0;
		list_for_each_entry(item, list, list) {
			if (!strcmp(item->name, argv[index])) {
				found = item;
				break;
			}
		}
		if (!found) {
			found = malloc(sizeof(*found));
			found->fun = 0;
			found->parent = parent;
			strncpy(found->name, argv[index], sizeof(found->name)-1);
			list_init(&found->children);
			list_insert(list, &found->list);
		}
		if (parent && parent->fun) {
			printf("error: \"%s\" conflict at \"%s\"\n", cmd, argv[index-1]);
		}
		list = &found->children;
		index++;
		parent = found;
	}
	found->fun = fun;
	free(line);
	return 0;
}

static int
cli_unregister(const char *cmd)
{
	char *line = strdup(cmd);
	char *argv[CLI_NARGS];
	int argc = str2argv(line, argv), index = 0;
	struct list_head *list = &cli;
	struct node *item, *found = 0, *parent;
	while (index < argc) {
		found = 0;
		list_for_each_entry(item, list, list) {
			if (!strcmp(item->name, argv[index])) {
				found = item;
				break;
			}
		}
		if (!found) {
			//printf("error: did not find \"%s\"\n", cmd);
			return 0;
		} else if (found->fun) {
			break;
		}

		list = &found->children;
		index++;
	}
	if (!found->fun) {
		//printf("error: \"%s\" is incomplete\n", cmd);
		return 0;
	}

	found->fun = 0; // dead
	do {
		if (!list_empty(&found->children))
			break;
		list_remove(&found->list);
		parent = found->parent;
		free(found);
		found = parent;
	} while (found);

	free(line);
	return 0;
}

int
cli_options(struct list_head *list, int argc, char *argv[])
{
	struct node *item;
	printf("  options:\n");
	list_for_each_entry(item, list, list) {
		printf("   ");
		for (int i = 0; i < argc; i++)
			printf(" %s", argv[i]);
		printf(" %s\n", item->name);
	}
	return 0;
}

int
cli_execute(const char *cmd)
{
	char *line = strdup(cmd);
	char *argv[CLI_NARGS];
	int argc = str2argv(line, argv), index = 0;
	struct list_head *list = &cli;
	struct node *item, *found;

	if (!argc)
		return 0;

	while (index < argc) {
		found = 0;
		list_for_each_entry(item, list, list) {
			if (!strcmp(item->name, argv[index])) {
				found = item;
				break;
			}
		}
		if (!found) {
			return cli_options(list, index, argv);
		} else if (found->fun) {
			break;
		}
		list = &found->children;
		index++;
	}
	if (!found->fun) {
		return cli_options(&found->children, index, argv);
	}
	int rc = found->fun(argc - index, argv + index);
	free(line);
	return rc;
}

int
register_cli(struct cli *v, size_t n)
{
	for (size_t i = 0; i < n; i++)
		cli_register(v[i].cmd, v[i].fun);
	return 0;
}

void
unregister_cli(struct cli *v, size_t n)
{
	for (size_t i = 0; i < n; i++)
		cli_unregister(v[i].cmd);
}


