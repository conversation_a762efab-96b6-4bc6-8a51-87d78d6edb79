#include <stdlib.h>
#include <assert.h>
#include <pcap.h>

#include "module.h"
#include "driver.h"
#include "device.h"
#include "packet.h"

struct pcap_device_packet {
	unsigned short len;
	unsigned char data[65535];
};

struct pcap_device {
	struct device dev;
	struct pcap_device_packet data;
	pcap_t *pcap;
};

struct pcap_device_req {
	struct pcap_device *pd;
	unsigned *vp;
	unsigned short vi, vs;
};

static time_t seconds;

int
pcap_clock_gettime(clockid_t id, struct timespec *tp)
{
	(void)id;
	tp->tv_sec = seconds;
	tp->tv_nsec = 0;
	return 0;
}

static void
dispatch_cb(u_char *user, const struct pcap_pkthdr *h, const u_char *bytes)
{
	struct pcap_device_req *req = (struct pcap_device_req *)user;
	struct pcap_device_packet *packet = &req->pd->data;
	packet->len = h->caplen;
	req->pd->dev.ts = (uint64_t)h->ts.tv_sec * 1000 + h->ts.tv_usec / 1000;
	seconds = h->ts.tv_sec;
	assert(packet->len < sizeof(req->pd->data));
	memcpy(packet->data, bytes, h->caplen);
	req->vp[req->vi++] = 0;
}

int
pcap_device_receive(struct device *dev, unsigned *vp, unsigned vs)
{
	(void)vp;
	(void)vs;
	int n;
	struct pcap_device *pd = container_of(dev, struct pcap_device, dev);
	struct pcap_device_req req = { .pd = pd, .vp = vp, .vs = vs };
	assert(pd->data.len == 0);
	n = pcap_dispatch(pd->pcap, 1, dispatch_cb, (void *)&req);
	if (n < 0)
		return 0;
	return n;
}

int
pcap_device_release(struct device *dev, unsigned *vp, unsigned vs)
{
	(void)vp;
	(void)vs;
	struct pcap_device *pd = container_of(dev, struct pcap_device, dev);
	assert(vs == 1 && vp[0] == 0);
	pd->data.len = 0;
	return 0;
}

int
pcap_device_packet(struct device *dev, unsigned id, struct packet *packet)
{
	(void)id;
	struct pcap_device *pd = container_of(dev, struct pcap_device, dev);
	assert(id == 0);
	packet->len = pd->data.len;
	packet->data = pd->data.data;
	packet->ts = dev->ts;
	return 0;
}

static void
pcap_device_periodic(struct device *dev)
{
	(void) dev;
}

static int
pcap_device_selectable_fd(struct device *dev)
{
	struct pcap_device *pd = container_of(dev, struct pcap_device, dev);
	return pcap_get_selectable_fd(pd->pcap);
}

static struct device *
pcap_device_create(const char *name)
{
	char errbuf[PCAP_ERRBUF_SIZE];
	struct pcap_device *pd = calloc(1, sizeof(*pd));
	struct device *dev = &pd->dev;
	dev->receive = pcap_device_receive;
	dev->release = pcap_device_release;
	dev->get_packet = pcap_device_packet;
	dev->periodic = pcap_device_periodic;
	dev->get_selectable_fd = pcap_device_selectable_fd;

	pd->data.len = 0;

	if (!(pd->pcap = pcap_open_offline(name, errbuf))) {
		pd->pcap = pcap_create(name, errbuf);
		if (!pd->pcap) {
			free(pd);
			return 0;
		}
		pcap_set_promisc(pd->pcap, 1);
		pcap_set_snaplen(pd->pcap, 1600);
		pcap_set_timeout(pd->pcap, 1);
		pcap_setnonblock(pd->pcap, 1, errbuf);
		pcap_activate(pd->pcap);
	} else {
		pal_gettime = pcap_clock_gettime;
	}
	register_device(dev);
	return dev;
}

static void
pcap_device_remove(struct device *dev)
{
	struct pcap_device *pd = container_of(dev, struct pcap_device, dev);
	pcap_close(pd->pcap);
	unregister_device(&pd->dev);
	free(pd);
}

static struct driver pcap_driver = {
	.name = "pcap",
	.create = pcap_device_create,
	.remove = pcap_device_remove
};

static int
pcap_module_init()
{
	return register_driver(&pcap_driver);
}

static void
pcap_module_exit()
{
	unregister_driver(&pcap_driver);
}

module_init(pcap_module_init);
module_exit(pcap_module_exit);
