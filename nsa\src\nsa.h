/**
 * @file nsa.h
 * @brief Network Security Application (NSA) - Main Header File
 * 
 * This file contains the core definitions, structures, and interfaces
 * for the NSA (Network Security Application) system.
 * 
 * NSA is a high-performance network security application that provides:
 * - Deep Packet Inspection (DPI) capabilities
 * - Policy-based access control  
 * - Session management and enrichment
 * - Integration with DPIF data plane library
 * 
 * <AUTHOR> Li
 * @date 2025
 * @copyright Calix Inc.
 */

#ifndef _NSA_H_
#define _NSA_H_

/* ========================================================================
 * Standard Library Includes
 * ======================================================================== */
#include <errno.h>
#include <pthread.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/un.h>
#include <time.h>
#include <unistd.h>

/* ========================================================================
 * External Library Includes
 * ======================================================================== */
#include "cdb/cdb.h"    /* Configuration Database */
#include <cmd_parser.h> /* CLI command parser */
#include <daemonlib.h>  /* Daemon framework */
#include <dpif.h>       /* Data Plane Interface */
#include <rte_mempool.h>
#include <sl_files.h> /* Schema and file utilities */

/* ========================================================================
 * NSA Core Module Includes
 * ======================================================================== */
#include "nsa_helper.h" /* Helper thread utilities */
#include "nsa_types.h"  /* Core type definitions */
#include "pml.h"

#ifdef __cplusplus
extern "C" {
#endif

/* =======================================================================
 * NSA Monitor definitions
 * ======================================================================= */
#define NSA_MONITOR_SOCKET_PATH "/tmp/nsa_monitor.sock"
#define MAX_MONITOR_CORES 8
#define MAX_MONITOR_SESSIONS 10
#define MAX_SESSIONS 200
#define MAX_MONITOR_LOGS 5

typedef struct {
    int lcore_id;
    char type[8];
    int cpu_usage;
    int rate;
    int offload_rate;
    int ring_usage;
    int ring_capacity;
    int sessions;
} nsa_monitor_core_stats_t;

typedef struct {
    char protocol[8];
    char src[128];
    char dst[128];
    char app[20];
    char alp[20];
    char user[20];
    char device[20];
    char verdict[20];
    int verdict_color_pair;
    char details[32];
    char icon[5];
    char threat[20];
} nsa_monitor_session_info_t;

typedef struct {
    int color_pair;
    char level[12];
    char message[256];
} nsa_monitor_event_log_t;

typedef struct {
    double total_bps;
    double total_pps;
    double total_cps;
    long long active_sessions;
    long long total_sessions_capacity;
    int session_timeouts;
    nsa_monitor_core_stats_t core_stats[MAX_MONITOR_CORES];
    nsa_monitor_session_info_t sessions[MAX_SESSIONS];
    nsa_monitor_event_log_t logs[MAX_MONITOR_LOGS];
} nsa_monitor_data_t;

/* ========================================================================
 * NSA Version and Build Information
 * ======================================================================== */
#define NSA_VERSION_MAJOR 1
#define NSA_VERSION_MINOR 0
#define NSA_VERSION_PATCH 0
#define NSA_VERSION_STRING "1.0.0"
#define NSA_BUILD_DATE __DATE__
#define NSA_BUILD_TIME __TIME__

/* ========================================================================
 * NSA Configuration Constants
 * ======================================================================== */
#define NSA_MAX_PROCESS_NAME_LEN 64
#define NSA_MAX_LOG_FILE_PATH 256
#define NSA_MAX_CONFIG_STRING 512
#define NSA_IPC_BUFFER_SIZE 0x500000 /* 5MB IPC buffer */

/* NSA daemon control flags */
#define NSA_FLAG_FORCE_START 0x00000001
#define NSA_FLAG_FOREGROUND 0x00000002
#define NSA_FLAG_DO_RESTART 0x00000004
#define NSA_FLAG_DO_SHUTDOWN 0x00000008
#define NSA_FLAG_INIT_RUN 0x00000010

/* ========================================================================
 * NSA PML Integration Constants
 * ======================================================================== */
#define NSA_THREAT_THRESHOLD 5                     /* Base threat detection threshold */
#define NSA_THREAT_BLOCK_THRESHOLD 8               /* High threat blocking threshold */
#define NSA_PML_CLASSIFY_FILE "etc/nsa/signatures.bin" /* Classification DPI program file */
#define NSA_PML_RULES_FILE "etc/nsa/ruleset.bin"         /* Rules evaluation program file */

/* ========================================================================
 * NSA PML Context Structure (Compatible with PAL)
 * ======================================================================== */
/**
 * @brief PML context structure for packet analysis
 * 
 * This structure defines the context data used by PML for pattern matching
 * and analysis. It must be compatible with PAL's context structure to ensure
 * proper interoperability with PML programs.
 * 
 * Layout must match exactly with PAL's struct context for PML compatibility.
 */
struct nsa_pml_context {
    uint8_t src[16];   /**< Source IP address (IPv4 or IPv6, network byte order) */
    uint8_t dst[16];   /**< Destination IP address (IPv4 or IPv6, network byte order) */
    uint16_t sport;    /**< Source port (host byte order) */
    uint16_t dport;    /**< Destination port (host byte order) */
    uint8_t proto;     /**< IP protocol (e.g., TCP=6, UDP=17) */
    uint8_t verdict;   /**< Verdict (input/output param for policy engine) */
    uint16_t szone;    /**< Source zone (security/route domain) */
    uint16_t dzone;    /**< Destination zone (security/route domain) */
    uint16_t group;    /**< Group ID (for policy grouping) */
    uint32_t user;     /**< User ID (for user-based policy) */
    uint64_t host;     /**< Hostname hash (for SNI/HTTP host, etc.) */
    uint8_t device[6]; /**< Device ID (client MAC address, 6 bytes) */
    uint16_t alp;      /**< Application Layer Protocol (e.g., HTTP, TLS) */
    uint16_t app;      /**< Detected application (e.g., Facebook, YouTube) */
    uint16_t dev;      /**< Device type (e.g., mobile, PC, IoT) */
    uint16_t geo;      /**< Geographic region code */
    uint16_t threat;   /**< Threat ID (malware, risk, etc.) */
    uint16_t cat;      /**< Traffic category (0 if app is set) */
    uint16_t date;     /**< Encoded datetime (for schedule/rule time) */
} __attribute__((packed));

/* Compile-time assertions to ensure structure layout compatibility with PAL */
_Static_assert(sizeof(struct nsa_pml_context) == 76, "nsa_pml_context size must be 76 bytes");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, src) == 0, "src offset must be 0");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, dst) == 16, "dst offset must be 16");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, sport) == 32, "sport offset must be 32");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, dport) == 34, "dport offset must be 34");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, proto) == 36, "proto offset must be 36");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, verdict) == 37, "verdict offset must be 37");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, szone) == 38, "szone offset must be 38");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, dzone) == 40, "dzone offset must be 40");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, group) == 42, "group offset must be 42");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, user) == 44, "user offset must be 44");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, host) == 48, "host offset must be 48");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, device) == 56, "device offset must be 56");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, alp) == 62, "alp offset must be 62");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, app) == 64, "app offset must be 64");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, dev) == 66, "dev offset must be 66");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, geo) == 68, "geo offset must be 68");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, threat) == 70, "threat offset must be 70");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, cat) == 72, "cat offset must be 72");
_Static_assert(__builtin_offsetof(struct nsa_pml_context, date) == 74, "date offset must be 74");

/* ========================================================================
 * NSA Core Data Structures
 * ======================================================================== */

/**
 * @brief NSA database handle structure
 * 
 * Contains handles to the various database components used by NSA
 * for storing session information and configuration data.
 */
typedef struct nsa_cdb_handle {
    cdb_db_h sub_db_ram;          /**< RAM-based database handle */
    cdb_table_h enriched_session; /**< Enriched session table handle */
} nsa_cdb_handle_t;

/**
 * @brief NSA root context structure
 * 
 * This is the main context structure that contains all the core
 * components and state information for the NSA application.
 */
typedef struct nsa_root {
    /* Daemon framework components */
    dl_context_t *daemon_ctx;    /**< Daemon context */
    dl_server_t *server_ctx;     /**< Server context */
    cp_context_t *cli_parser;    /**< CLI command parser */
    sl_schema_set_t *schema_set; /**< Schema definitions */
    dl_timer timer_ctx;          /**< Timer context */

    /* Process identification */
    char process_name[NSA_MAX_PROCESS_NAME_LEN]; /**< Process name */
    char log_file_path[NSA_MAX_LOG_FILE_PATH];   /**< Log file path */

    /* Runtime state */
    uint32_t control_flags; /**< Control flags */
    bool is_running;        /**< Running state flag */
    time_t start_time;      /**< Process start time */

    /* Database handle */
    nsa_cdb_handle_t cdb_handle; /**< Database handle */

    struct rte_mempool *nsa_session_ctx_pool;

    /* Monitor thread */
    pthread_t monitor_thread_id;
    int monitor_socket_fd;
    nsa_monitor_data_t monitor_data;
    pthread_mutex_t monitor_data_mutex;

    /* Extension interfaces (for future enhancements) */
    void *dpi_context;     /**< DPI engine context (reserved) */
    void *policy_context;  /**< Policy engine context (reserved) */
    void *threat_context;  /**< Threat intel context (reserved) */
    void *logging_context; /**< Logging system context (reserved) */

} nsa_root_t;

/**
 * @brief Global NSA root context
 * 
 * This is the global instance of the NSA root context that is
 * accessible throughout the application.
 */
extern nsa_root_t *g_nsa_root;

/* ========================================================================
 * NSA PML Context Structure
 * ======================================================================== */
/**
 * @brief PML context structure for NSA sessions
 * 
 * This structure maintains PML state and analysis results for each
 * session managed by NSA. It extends the basic PML context with
 * NSA-specific threat analysis and classification data.
 */
typedef struct nsa_session_context {
    struct pml *pml_classify_instance;  /**< PML instance for classification (classify.bin) */
    struct pml *pml_rules_instance;     /**< PML instance for rule evaluation (control.bin) */
    struct nsa_pml_context pml_context; /**< NSA PML context data (compatible with PAL) */
    uint32_t rule_state;                /**< Current rule evaluation state (0 if no rules loaded) */
    uint64_t last_analysis_time;        /**< Last packet analysis timestamp */
    uint8_t threat_level;               /**< Current threat level (0-10) */
    uint16_t app_classification;        /**< Application classification result */
    uint16_t category;                  /**< Traffic category classification */
    uint32_t packets_analyzed;          /**< Number of packets analyzed */
    uint64_t bytes_analyzed;            /**< Total bytes analyzed */
    uint8_t analysis_complete;          /**< Flag: 1 if analysis is complete */
    char app_name[16];                  /**< Identified application name */
    char alp_name[16];                  /**< Identified application protocol name */
    char server_name[256];              /**< Server name (hostname) extracted from PML watchpoint */
    bool first_packet_reversed;         /**< true if first packet's direction was opposite to canonical key */
} nsa_session_context_t;

/* ========================================================================
 * NSA PML Global Variables
 * ======================================================================== */

/**
 * @brief Master PML instances for session cloning
 * 
 * These are the master PML instances that contain the loaded DPI programs.
 * Individual session PML instances are cloned from these master instances
 * for efficient memory usage and shared program code.
 */
extern struct pml *nsa_master_classify_pml; /**< Master PML instance for classification (classify.bin) */
extern struct pml *nsa_master_rules_pml;    /**< Master PML instance for rules evaluation (control.bin) */

/* ========================================================================
 * NSA Logging System
 * ======================================================================== */

/**
 * @brief NSA log levels (aligned with syslog standards)
 */
typedef enum {
    NSA_LOG_EMERGENCY = 0, /**< System unusable */
    NSA_LOG_ALERT = 1,     /**< Alert conditions (immediate action required) */
    NSA_LOG_CRITICAL = 2,  /**< Critical conditions */
    NSA_LOG_ERROR = 3,     /**< Error conditions */
    NSA_LOG_WARNING = 4,   /**< Warning conditions */
    NSA_LOG_NOTICE = 5,    /**< Notable conditions */
    NSA_LOG_INFO = 6,      /**< General information */
    NSA_LOG_DEBUG = 7      /**< Debug information */
} nsa_log_level_t;

/**
 * @brief NSA log categories for event classification
 */
typedef enum {
    NSA_LOG_CAT_SYSTEM = 0x01,      /**< System operations */
    NSA_LOG_CAT_SECURITY = 0x02,    /**< Security events */
    NSA_LOG_CAT_NETWORK = 0x04,     /**< Network operations */
    NSA_LOG_CAT_PERFORMANCE = 0x08, /**< Performance metrics */
    NSA_LOG_CAT_AUDIT = 0x10,       /**< Audit trail */
    NSA_LOG_CAT_CONFIG = 0x20,      /**< Configuration changes */
    NSA_LOG_CAT_THREAT = 0x40,      /**< Threat detection */
    NSA_LOG_CAT_COMPLIANCE = 0x80,  /**< Compliance events */
    NSA_LOG_CAT_ALL = 0xFF          /**< All categories */
} nsa_log_category_t;

/* Logging macros */
#define NSA_LOG_EMERGENCY(fmt, ...) \
    dl_log_print(NSA_LOG_EMERGENCY, "[EMERG] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define NSA_LOG_ALERT(fmt, ...) \
    dl_log_print(NSA_LOG_ALERT, "[ALERT] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define NSA_LOG_CRITICAL(fmt, ...) \
    dl_log_print(NSA_LOG_CRITICAL, "[CRIT]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define NSA_LOG_ERROR(fmt, ...) \
    dl_log_print(NSA_LOG_ERROR, "[ERROR] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define NSA_LOG_WARNING(fmt, ...) \
    dl_log_print(NSA_LOG_WARNING, "[WARN]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define NSA_LOG_NOTICE(fmt, ...) \
    dl_log_print(NSA_LOG_NOTICE, "[NOTE]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define NSA_LOG_INFO(fmt, ...) dl_log_print(NSA_LOG_INFO, "[INFO]  %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#define NSA_LOG_DEBUG(fmt, ...) \
    dl_log_print(NSA_LOG_DEBUG, "[DEBUG] %s:%d: " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

/* Convenience macros */
#define __F__ __FUNCTION__
#define __L__ __LINE__

/* ========================================================================
 * NSA Core API Functions
 * ======================================================================== */

/**
 * @brief Initialize the NSA application
 * 
 * Initializes all core components of the NSA application including
 * daemon framework, database, session management, and helper threads.
 * 
 * @param[in] argc  Command line argument count
 * @param[in] argv  Command line argument vector
 * @return 0 on success, negative error code on failure
 */
int nsa_init(int argc, char *argv[]);

/**
 * @brief Cleanup and shutdown the NSA application
 * 
 * Gracefully shuts down all NSA components and releases resources.
 */
void nsa_cleanup(void);

/**
 * @brief Get the NSA version string
 * 
 * @return Pointer to version string
 */
const char *nsa_get_version(void);

/**
 * @brief Get NSA runtime statistics
 * 
 * @param[out] stats  Pointer to statistics structure to fill
 * @return 0 on success, negative error code on failure
 */
int nsa_get_runtime_stats(void *stats); /* TODO: Define stats structure */

/**
 * @brief Register NSA session callbacks with DPIF
 * 
 * Registers the NSA session handling callbacks with the DPIF library
 * to enable session management and packet analysis integration.
 */
void nsa_session_register_callbacks(void);

/* ========================================================================
 * NSA PML Integration Functions
 * ======================================================================== */

/**
 * @brief Initialize PML engine for NSA
 * 
 * Initializes the PML (Packet Matching Language) engine and loads
 * the NSA-specific DPI program for deep packet inspection.
 * 
 * @return 0 on success, negative error code on failure
 */
int nsa_pml_init(void);

/**
 * @brief Cleanup PML engine resources
 * 
 * Releases all PML-related resources and cleans up the master
 * PML instance.
 */
void nsa_pml_cleanup(void);

/**
 * @brief Create NSA session context with PML integration
 * 
 * Creates and initializes a new NSA session context with PML
 * instance and analysis state for the given session.
 * 
 * @param[in] flow_info  Flow information for the session
 * @param[in] session_id DPIF session descriptor
 * @return Pointer to allocated context, or NULL on failure
 */
nsa_session_context_t *nsa_create_session_context(const struct dpif_flow_info *flow_info, int session_id);

/**
 * @brief Destroy NSA session context
 * 
 * Cleans up and deallocates the NSA session context including
 * the associated PML instance and resources.
 * 
 * @param[in] nsa_ctx NSA session context to destroy
 */
void nsa_destroy_session_context(nsa_session_context_t *nsa_ctx);

/**
 * @brief Populate PML context from flow information
 * 
 * Fills the PML context structure with network flow information
 * extracted from the DPIF flow info.
 * 
 * @param[out] ctx       NSA PML context to populate
 * @param[in]  flow_info DPIF flow information
 */
void nsa_populate_pml_context(struct nsa_pml_context *ctx, const struct dpif_flow_info *flow_info);

/**
 * @brief Evaluate PML analysis results
 * 
 * Processes the results from PML analysis and updates the NSA
 * session context with threat assessment and application classification.
 * 
 * @param[in] nsa_ctx    NSA session context
 * @param[in] session_id DPIF session descriptor
 * @return 0 on success, negative error code on failure
 */
int nsa_evaluate_pml_results(nsa_session_context_t *nsa_ctx, int session_id);

/**
 * @brief Get session verdict based on analysis
 * 
 * Determines the final verdict for a session based on threat level
 * and application classification results.
 * 
 * @param[in] nsa_ctx NSA session context
 * @return DPI verdict code
 */
dpi_verdict_t nsa_get_session_verdict(nsa_session_context_t *nsa_ctx);

/**
 * @brief Check if background work should be offloaded
 * 
 * Determines whether the current session analysis requires
 * background processing on worker threads.
 * 
 * @param[in] nsa_ctx NSA session context
 * @return 1 if work should be offloaded, 0 otherwise
 */
int nsa_should_offload_work(nsa_session_context_t *nsa_ctx);

/**
 * @brief Schedule background work for session
 * 
 * Creates and schedules background work for intensive analysis
 * tasks that should not block the main data path.
 * 
 * @param[in] session_id DPIF session descriptor
 * @param[in] nsa_ctx    NSA session context
 * @return 0 on success, negative error code on failure
 */
int nsa_schedule_background_work(int session_id, nsa_session_context_t *nsa_ctx);

/**
 * @brief Initialize NSA CDB handle and all database components
 * @param[in] root  Pointer to NSA root context
 * @return 0 on success, negative error code on failure
 */
int nsa_cdb_init(nsa_root_t *root);

/* ========================================================================
 * NSA Monitor Functions
 * ======================================================================== */
int nsa_monitor_thread_start(nsa_root_t *root);
void nsa_monitor_thread_stop(nsa_root_t *root);

/* ========================================================================
 * NSA Extension Interface (Future Enhancements)
 * ======================================================================== */

/**
 * @brief Extension module registration interface
 * 
 * This interface allows registration of extension modules for
 * enhanced functionality such as advanced DPI, policy engines,
 * threat intelligence, and logging systems.
 */
typedef struct nsa_extension_interface {
    const char *module_name;    /**< Module name */
    const char *module_version; /**< Module version */
    int (*init_func)(void);     /**< Initialization function */
    void (*cleanup_func)(void); /**< Cleanup function */
    void *module_context;       /**< Module-specific context */
} nsa_extension_interface_t;

/**
 * @brief Register an extension module
 * 
 * @param[in] extension  Pointer to extension interface structure
 * @return 0 on success, negative error code on failure
 */
int nsa_register_extension(const nsa_extension_interface_t *extension);

/**
 * @brief Unregister an extension module
 * 
 * @param[in] module_name  Name of the module to unregister
 * @return 0 on success, negative error code on failure
 */
int nsa_unregister_extension(const char *module_name);

void nsa_log_event_simple(const char* format, ...);
void nsa_log_get_latest(nsa_monitor_event_log_t* logs_dest, int max_logs);

#ifdef __cplusplus
}
#endif

#endif /* _NSA_H_ */