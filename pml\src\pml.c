#define _GNU_SOURCE
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <inttypes.h>
#include <string.h>
#include <assert.h>
#include <ctype.h>
#include <errno.h>
#include <time.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/mman.h>
#include <sys/stat.h>

#include "pml.h"
#include "bpf.h"

#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))
#define KEY(slot, i)	(slot[i * 2])
#define VAL(slot, i)	(slot[i * 2 + 1])
#define T(plus) (pml_gettime() + (plus))

#define pml_map_for_each_entry(m, k, v) \
	for (uint32_t i = 0, n = (m)->size; n; i++) \
		if ((k = &(KEY((m)->slots, i)))->type > PML_EMPTY && (v=k + 1, --n, 1))

/* Let linking application override this */
__attribute__((weak)) int
pml_gettime()
{
	struct timespec ts;
	clock_gettime(CLOCK_MONOTONIC, &ts);
	return (ts.tv_sec + ts.tv_nsec / 1e9);
}

static struct pml_obj *pml_map_set(struct pml_map *m, struct pml_obj *key, struct pml_obj *val);
static struct pml_obj *pml_map_get(struct pml_map *m, struct pml_obj *key, enum pml_type def);
static int             pml_map_del(struct pml_map *m, struct pml_obj *key);

static uint32_t
pml_obj_hash(struct pml_obj *v, uint32_t cap)
{
	uint32_t h = 2166136261u;
	const uint32_t mask = cap - 1;

	if (v->type == PML_INT) {
		if ((uint32_t)v->v.i < cap) {
			return (uint32_t)(v->v.i & 0xffffffff);
		}
		/* MurmurHash3Mixer */
		uint64_t h = (uint64_t)v->v.i;
		h ^= h >> 33;
		h *= 0xff51afd7ed558ccdULL;
		h ^= h >> 33;
		h *= 0xc4ceb9fe1a85ec53ULL;
		h ^= h >> 33;
		return (uint32_t)h & mask;
	}

	switch (v->type) {
	case PML_BUF:
		for (uint16_t i = v->offset; i < v->length; i++) {
			h ^= v->v.b->data[i];
			h *= 16777619u;
		}
		break;
	case PML_MAP:
		/*
		uint8_t *p = (uint8_t *)&v->v.m;
		for (int i = 0; i < sizeof(struct pml_map *); i++) {
			h ^= p[i];
			h *= 16777619u;
		}
		*/
		break;
	default:
		return 0;
	}
	return h & mask;
}

static inline int
pml_obj_eq(struct pml_obj *a, struct pml_obj *b)
{
	if (a->type != b->type) return 0;
	switch (a->type) {
	case PML_INT:
		return a->v.i == b->v.i;
	case PML_BUF:
		if (a->length != b->length)
			return 0;
		return memcmp(a->v.b->data + a->offset, b->v.b->data + b->offset, a->length) == 0;
	case PML_MAP:
		return a->v.m == b->v.m;
	default:
		return 0;
	}
}

static void
pml_obj_acquire(struct pml_obj *v)
{
	switch (v->type) {
	case PML_BUF:
		v->v.b->ref++;
		break;
	case PML_MAP:
		v->v.m->ref++;
		break;
	default:
		break;
	}
}

static void pml_buf_release(struct pml_buf *);
static void pml_map_release(struct pml_map *);

static void
pml_obj_release(struct pml_obj *v)
{
	switch (v->type) {
	case PML_BUF:
		pml_buf_release(v->v.b);
		break;
	case PML_MAP:
		pml_map_release(v->v.m);
		break;
	default:
		break;
	}
//	v->type = PML_EMPTY;
}

static struct pml_buf *pml_buf();
static struct pml_map *pml_map(uint32_t);

static inline struct pml_obj
pml_obj(enum pml_type type)
{
	return (struct pml_obj) { .type = type, .offset = 0, .length = 0, .expires = 0, .v.i = 0 };
}

static struct pml_obj
pml_obj_new(enum pml_type type)
{
	struct pml_obj res = pml_obj(type);
	switch (type) {
	case PML_INT:
		res.v.i = 0;
		break;
	case PML_BUF:
		res.v.b = pml_buf();
		break;
	case PML_MAP:
		res.v.m = pml_map(8);
		break;
	default:
		assert(0);
	}

	return res;
}

static inline struct pml_obj
pml_int(int64_t i)
{
	struct pml_obj res = pml_obj(PML_INT);
	res.v.i = i;
	return res;
}

static inline struct pml_buf *
pml_buf_acquire(struct pml_buf *b)
{
	b->ref++;
	return b;
}

static void
pml_buf_release(struct pml_buf *b)
{
	assert(b->ref != 0);
	if (--b->ref > 0)
		return;
	if (b->free)
		free(b->data);
	free(b);
}

static inline void
pml_map_clear(struct pml_map *m)
{
	struct pml_obj *key, *val;
	pml_map_for_each_entry(m, key, val) {
		pml_obj_release(key);
		pml_obj_release(val);
	}
	m->size = 0;
}

static inline struct pml_map *
pml_map_acquire(struct pml_map *m)
{
	m->ref++;
	return m;
}

static void
pml_map_release(struct pml_map *m)
{
	assert(m->ref != 0);
	if (--m->ref > 0)
		return;
	pml_map_clear(m);
	free(m->slots);
	free(m);
}

static inline uint32_t
next_pow_2(uint32_t n)
{
	if (n < 2) return 1;
	return 1 << (32 - __builtin_clz(n - 1));
}

static struct pml_map *
pml_map(uint32_t cap)
{
	struct pml_map *m;
	m = malloc(sizeof(*m));
	m->cap = next_pow_2(cap);
	m->size = 0;
	m->ref = 1;
	m->slots = calloc(m->cap * 2, sizeof(struct pml_obj));
	m->index = 0;
	m->parent = NULL;
	assert(m->slots);
	return m;
}

/* pml_map_clone()
 *
 * Clone a map. This does not acquire @map, but will acquire any reference
 * counted object contained within it.
 *
 * Returns the new map or NULL if the map could not be allocated.
 */
static struct pml_map *
pml_map_clone(struct pml_map *map)
{
	uint32_t i, n;
	const uint32_t size = map->size;
	struct pml_obj *m, *c;
	struct pml_map *res = pml_map(map->cap);
	if (!res)
		return 0;
	m = map->slots;
	c = res->slots;
	for (i = 0, n = 0; n < size; i++) {
		if (KEY(m, i).type <= PML_EMPTY)
			continue;
		KEY(c, i) = KEY(m, i);
		VAL(c, i) = VAL(m, i);
		pml_obj_acquire(&KEY(c, i));
		pml_obj_acquire(&VAL(c, i));
		++n;
	}
	res->size = size;
	return res;
}

static inline int
needs_upsize(struct pml_map *m)
{
	return (m->size >= m->cap * 0.75);
}

static void
pml_map_displace(struct pml_map *m, uint32_t start, struct pml_obj *key, struct pml_obj *val)
{
	uint32_t i;

	for (i = start;; i = (i + 1) & (m->cap-1)) {
		if (KEY(m->slots, i).type == PML_EMPTY || KEY(m->slots, i).type == PML_DELETED) {
			KEY(m->slots, i) = *key;
			VAL(m->slots, i) = *val;
			return;
		}
	}
}

/* pml_map_reclaim()
 *
 * Starting from PML_DELETED index @i, this function searches right for
 * non-tombstoned slots. If it encounters a sentinel slot (PML_EMPTY), it
 * retraces left to promote PML_DELETED slots to PML_EMPTY.
 *
 * This operation is not required, but added as a housekeeping task to
 * help reduce probe length on large fragmented maps.
 */
static void
pml_map_reclaim(struct pml_map *m, uint32_t i)
{
	/* Search right for any non-tombstone slot */
	do {
		i = (i + 1) & (m->cap - 1);
	} while (KEY(m->slots, i).type == PML_DELETED);

	if (KEY(m->slots, i).type != PML_EMPTY)
		return;

	/* Promote left adjacent tombstones to sentinels */
	do {
		i = (i - 1) & (m->cap - 1);
		if (KEY(m->slots, i).type != PML_DELETED)
			break;
		KEY(m->slots, i).type = PML_EMPTY;
#ifdef OPTION_TEST
		stats.left_adjacent++;
#endif
	} while (1);
}


static inline int
pml_map_expire(struct pml_map *m, uint32_t i, uint32_t time)
{
	struct pml_obj *val = &VAL(m->slots, i);
	if (val->expires && val->expires <= time) {
		pml_obj_release(&KEY(m->slots, i));
		pml_obj_release(&VAL(m->slots, i));
		KEY(m->slots, i).type = PML_DELETED;
		m->size--;
		pml_map_reclaim(m, i);
#ifdef OPTION_TEST
		stats.value_expired++;
#endif
		return 1;
	}
	return 0;
}

/* pml_map_find()
 *
 * This is the master lookup function used by other internal API. This function
 * implements the probe algorithm, modifying the container as necessary to
 * support the perfect hash strategy, displacement, best tombstone selection,
 * expiry, and left adjacency reclaimation.
 *
 * Returns the index where the key may be located. It is the responsbility of
 * the caller to check if the cell is occupied or not.
 */
static uint32_t
pml_map_find(struct pml_map *m, struct pml_obj *key)
{
	const uint32_t h = pml_obj_hash(key, m->cap);
	const uint32_t time = T(0);
	uint32_t i = h;
	int r = -1;

	/* Check for perfect hash candidate before probing */
	if (key->v.i == h) {
		/* Perfect match already */
		if (pml_obj_eq(&KEY(m->slots, h), key)) {
#ifdef OPTION_TEST
			stats.perfect_matching++;
#endif
			pml_map_expire(m, i, time);
			return h;
		/* Perfect position available */
		} else if (KEY(m->slots, h).type <= PML_EMPTY) {
#ifdef OPTION_TEST
			stats.perfect_available++;
#endif
			return h;
		/* Perfect position is in use so it must be displaced */
		} else {
			pml_map_displace(m, (h + 1) & (m->cap -1), &KEY(m->slots, h), &VAL(m->slots, h));
			KEY(m->slots, h).type = PML_DELETED;
#ifdef OPTION_TEST
			stats.perfect_displaced++;
#endif
			return h;
		}
	}

	/* Probe loop */
	do {
#ifdef OPTION_TEST
		stats.probe_loops++;
#endif
		/* Equal */
		if (pml_obj_eq(&KEY(m->slots, i), key)) {
			/* Not expired */
			if (!pml_map_expire(m, i, time)) {
				/* Better position available */
				if (r != -1) {
					KEY(m->slots, r) = KEY(m->slots, i);
					VAL(m->slots, r) = VAL(m->slots, i);
					KEY(m->slots, i).type = PML_DELETED;
					i = r;
#ifdef OPTION_TEST
					stats.probe_relocation++;
#endif
				}
#ifdef OPTION_TEST
				stats.probe_found++;
#endif
				/* Return position */
				return i;
			}
#ifdef OPTION_TEST
			stats.value_expired++;
#endif
			/* Expired */
			break;

		/* Sentinal - Item not found */
		} else if (KEY(m->slots, i).type == PML_EMPTY) {
#ifdef OPTION_TEST
			stats.probe_sentinel++;
#endif
			break;

		/* Expired? */
		} else if (KEY(m->slots, i).type != PML_DELETED) {
			if (pml_map_expire(m, i, time)) {
#ifdef OPTION_TEST
				stats.value_expired++;
#endif
				if (r == -1) r = i;
			}

		/* Best tombstone */
		} else if (r == -1) {
#ifdef OPTION_TEST
			stats.probe_tombstone++;
#endif
			r = i;
		}

		/* Next */
		i = (i + 1) & (m->cap - 1);
	} while (i != h);

#ifdef OPTION_TEST
	stats.probe_count++;
#endif
	if (r != -1) {
		if (KEY(m->slots, i).type == PML_EMPTY) {
			/* Deleted slots left adjacent to a sentinel can be
			 * promoted to sentinels */
			do {
				i = (i - 1) & (m->cap - 1);
				if (KEY(m->slots, i).type != PML_DELETED)
					break;
				KEY(m->slots, i).type = PML_EMPTY;
#ifdef OPTION_TEST
				stats.left_adjacent++;
#endif
			} while (i != (uint32_t)r);
		}
		i = r;
	}

	return i;
}

/* pml_rep_update()
 *
 * Nested maps (map values) hold a weak reference to their parent so the replication
 * path can be computed quickly. This function updates those references.
 */
static inline void
pml_rep_update(struct pml_map *m, uint32_t i)
{
	if (m->index && VAL(m->slots, i).type == PML_MAP) {
		VAL(m->slots, i).v.m->index = i;
		VAL(m->slots, i).v.m->parent = m;
	}
}

/* pml_map_get()
 *
 * Lookup the value for the given key position @key. If the key does not already
 * exist, pml_map_get() will allocate and insert an empty instance of the value
 * according to the type defined by @def.
 *
 * Returns a pointer to the object found (or inserted). When pml_map_get() is
 * called with def<=PML_EMPTY, the function behaves like a try-get and may return
 * NULL if the key does not exist.
 */
static struct pml_obj *
pml_map_get(struct pml_map *m, struct pml_obj *key, enum pml_type def)
{
	struct pml_obj val, *res = NULL;
	uint32_t i = pml_map_find(m, key);

	/* Found */
	if (KEY(m->slots, i).type > PML_EMPTY) {
		pml_rep_update(m, i);
		res = &VAL(m->slots, i);
	/* Allocate a new item */
	} else if (def != PML_EMPTY) {
		val = pml_obj_new(def);

		/* Slower */
		if (needs_upsize(m)) {
			res = pml_map_set(m, key, &val);
			pml_obj_release(res); // res->type = def;
		/* Better */
		} else {
			KEY(m->slots, i) = *key; pml_obj_acquire(key);
			VAL(m->slots, i) = val; /* skip acquire on value; it was just allocated */
			res = &VAL(m->slots, i);
			pml_rep_update(m, i);
			m->size++;
		}
	}
	return res;
}

/* pml_map_try_emplace()
 *
 * This function attempts to insert value @val at key position @key if and only if the
 * key it not present.
 *
 * Returns 0 when the key already exists, and 1 when the key is inserted. In both cases
 * the out param @res is updated to point to the old or new value respectively.
 */
static uint8_t
pml_map_try_emplace(struct pml_map *m, struct pml_obj *key, struct pml_obj *val, struct pml_obj **res)
{
	uint32_t i = pml_map_find(m, key);

	if (KEY(m->slots, i).type > PML_EMPTY) {
		*res = &VAL(m->slots, i);
		return 0;
	}

	*res = pml_map_set(m, key, val);
	return 1;
}

/* pml_map_upsize()
 *
 * Upsize the map @m to the next power of 2 size.
 *
 * Returns a pointer to the new map or NULL if the operation failed.
 */
static struct pml_map *
pml_map_upsize(struct pml_map *m)
{
	const uint32_t capacity = m->cap << 1;
	struct pml_map next = {
		.cap = capacity,
		.size = 0,
		.ref = 1,
		.slots = calloc(capacity * 2, sizeof(struct pml_obj))
	};
	if (next.slots) {
		uint32_t i, n = m->size;
		for (i = 0; n; i++) {
			if (KEY(m->slots, i).type > PML_EMPTY) {
				pml_map_set(&next, &KEY(m->slots, i), &VAL(m->slots, i));
				pml_obj_release(&KEY(m->slots, i));
				pml_obj_release(&VAL(m->slots, i));
				--n;
			}
		}
		free(m->slots);
		m->slots = next.slots;
		m->cap   = next.cap;
		m->size  = next.size;
		return m;
	}
	return NULL;
}

/* pml_map_set()
 *
 * Insert or update the value @val at position @key in map @m.
 *
 * Positive integers that fall within the maps capacity are prioritized and
 * stored in their perfect hash position. If an existing item already occupies
 * the position, it is moved.
 *
 * Returns a pointer to the stored value.
 */
static struct pml_obj *
pml_map_set(struct pml_map *m, struct pml_obj *key, struct pml_obj *val)
{
	uint32_t i = pml_map_find(m, key);
	/* Update */
	if (KEY(m->slots, i).type > PML_EMPTY) {
		pml_obj_release(&VAL(m->slots, i));
		VAL(m->slots, i) = *val;
		pml_obj_acquire(val);
	/* Insert */
	} else {
		if (needs_upsize(m)) {
			if (!pml_map_upsize(m))
				return NULL;
			i = pml_map_find(m, key);
		}

		KEY(m->slots, i) = *key;
		VAL(m->slots, i) = *val;
		pml_obj_acquire(key);
		pml_obj_acquire(val);
		m->size++;
	}
	/* Replication  */
	pml_rep_update(m, i);
	return &VAL(m->slots, i);
}

/* pml_map_del()
 *
 * Deletes the entry from the map @m at key @k.
 */
static int
pml_map_del(struct pml_map *m, struct pml_obj *key)
{
	uint32_t i = pml_map_find(m, key);
	if (KEY(m->slots, i).type > PML_EMPTY) {
		pml_obj_release(&KEY(m->slots, i));
		pml_obj_release(&VAL(m->slots, i));
		KEY(m->slots, i).type = PML_DELETED;
		m->size--;
		pml_map_reclaim(m, i);
#ifdef OPTION_TEST
		stats.value_deleted++;
#endif
		return 1;
	}
	return 0;
}

static struct pml_obj *
pml_map_path_set(struct pml_map *env, struct pml_map *path, struct pml_obj *val)
{
	uint32_t i;
	struct pml_obj key;
	struct pml_map *m = env;

	for (i = 0; i < path->size - 1; i++) {
		key = pml_int(VAL(path->slots, i).v.i);
		struct pml_obj *r = pml_map_get(m, &key, PML_MAP);
		m = r->v.m;
	}

	key = pml_int(VAL(path->slots, i).v.i);
	return pml_map_set(m, &key, val);
}

static int
pml_map_path_del(struct pml_map *env, struct pml_map *path)
{
	uint32_t i;
	struct pml_obj key = pml_obj(PML_INT);
	struct pml_map *m = env;

	for (i = 0; i < path->size - 1; i++) {
		key.v.i = VAL(path->slots, i).v.i;
		struct pml_obj *r = pml_map_get(m, &key, PML_MAP);
		m = r->v.m;
	}

	key.v.i = VAL(path->slots, i).v.i;
	return pml_map_del(m, &key);
}

static struct pml_buf *
pml_buf()
{
	struct pml_buf *b = malloc(sizeof(*b));
	b->data = b->_data;
	b->size = 0;
	b->free = 0;
	b->ref = 1;
	return b;
}

static struct pml_obj
pml_buf_dup(const uint8_t *data, uint32_t size)
{
	struct pml_obj res = pml_obj(PML_BUF);
	struct pml_buf *b = malloc(sizeof(*b) + size);
	if (data)
		memcpy(b->_data, data, size);
	b->data = b->_data;
	b->size = size;
	b->free = 0;
	b->ref = 1;

	res.length = size;
	res.v.b = b;
	return res;
}

static struct pml_obj
pml_buf_cat(struct pml_buf *a, struct pml_buf *b)
{
	struct pml_obj res = pml_buf_dup(NULL, a->size + b->size);
	memcpy(res.v.b->data,           a->data, a->size);
	memcpy(res.v.b->data + a->size, b->data, b->size);
	return res; 
}

struct pml_obj
pml_str_dup(const char *s)
{
	return pml_buf_dup((const uint8_t *)s, strlen(s));
}

static struct pml_obj
pml_buf_ref(struct pml_buf *buf, const uint8_t *data, uint32_t len)
{
#if 0
	struct pml_obj res = pml_obj(PML_BUF);
	buf->data = (uint8_t *)data;
	buf->size = len;
	res.length = len;
	res.v.b = pml_buf_acquire(buf);
	return res;
#endif
	if (!buf) {
		buf = pml_buf();
	}
	buf->data = (uint8_t *)data;
	buf->size = len;
	return (struct pml_obj){ .type = PML_BUF, .offset = 0, .length = len, .expires = 0, .v.b = buf };
}

static inline int
resident(struct pml_obj *obj)
{
	assert(obj->type == PML_BUF);
	return obj->v.b->data == obj->v.b->_data;
}

#if 1
#define PML_TRACE(fmt, ...)
#define PML_VM_TRACE_CALL(r1, r2, r3, r4, r5, pml) \
	(void) (r1); \
	(void) (r2); \
	(void) (r3); \
	(void) (r4); \
	(void) (r5); \
	(void) (pml)
#else
#define PML_TRACE(fmt, ...) \
	printf(fmt, __VA_ARGS__);
#define PML_VM_TRACE_CALL(r1, r2, r3, r4, r5, pml) \
	PML_TRACE("%s(%zu,%zu,%zu,%zu,%zu) (context=%p)\n", __FUNCTION__, r1, r2, r3, r4, r5, pml);
#endif

enum pml_yield_type {
	PML_YIELD_NONE,
	PML_YIELD_SKIP,
	PML_YIELD_YANK,
	PML_YIELD_DATA
};

static inline uint16_t
offset(const struct pml *pml)
{
	if (!pml->rscan)
		return pml->buf.offset;
	return -pml->buf.offset;
}

static inline uint16_t
remaining(const struct pml *pml)
{
	if (!pml->rscan)
		return pml->buf.length - pml->buf.offset;
	return pml->buf.length + pml->buf.offset;
}

static inline uint16_t
yanking(const struct pml *pml)
{
	return pml->yield == PML_YIELD_YANK;
}

static inline uint16_t
skipping(const struct pml *pml)
{
	return pml->yield == PML_YIELD_SKIP;
}

/*
 * Garbage Collection
 *
 * The virtual environment can callout and request new objects. Currently this
 * is only implemented for maps. We don't know if this is temporary memory, or
 * if it will end up stored later in the global environment. To prevent leaks
 * during development, the garbage collector tracks allocations that need to
 * have have their reference dropped after the codeblock runs. Ideally the
 * compiler generates temporaries and can instructment the necessary operations
 * so perhaps later this can be removed.
 */

/* pml_gc_collect()
 */
void
pml_gc_collect(struct pml_map *gc, struct pml_obj *val)
{
	if (val->type != PML_INT) {
		// enum pml_type t = val->type; // save
		struct pml_obj key = pml_int(gc->size);
		pml_map_set(gc, &key, val);
		pml_obj_release(val);
		//val->type = t; // restore
		assert(val->v.m->ref == 1);
	}
}

/* pml_gc_cleanup()
 */
void
pml_gc_cleanup(struct pml_map *gc)
{
	while (gc->size) {
		struct pml_obj key = pml_int(gc->size-1);
		int ret = pml_map_del(gc, &key);
		assert(ret == 1);
	}
}

/*
 * JSON Serialization/Deserialization
 *
 * The three pml data types can be serialized to JSON. Currently the only case
 * for this is map replication. Any object may be serialized with pml_serialize()
 * which acts similar to json.dumps(). Deserialization from a c string to a
 * pml_obj occurs with pml_deserialize(), which acts like json.loads().
 */

static int
pml_obj_checksize(struct pml_obj *obj, size_t needed)
{
	size_t remaining = obj->length - obj->offset;
	if (needed > remaining) {
		uint8_t *data;
		size_t size = next_pow_2(obj->length + needed);
		if (!(data = realloc(obj->v.b->data, size)))
			return -ENOMEM;
		obj->v.b->data = data;
		obj->v.b->size = size;
		obj->v.b->free = 1;
		obj->length = size;
	}
	return 0;
}

static int
pml_dump_chr(int chr, struct pml_obj *dst)
{
	if (pml_obj_checksize(dst, 1))
		return -ENOMEM;
	dst->v.b->data[dst->offset++] = chr;
	return 0;
}

static int
pml_dump_int(struct pml_obj *obj, struct pml_obj *dst)
{
	size_t rlen = snprintf(NULL, 0, "%" PRId64, obj->v.i) + 1;
	if (pml_obj_checksize(dst, rlen))
		return -ENOMEM;
	dst->offset += snprintf((char *)(dst->v.b->data + dst->offset), dst->length - dst->offset, "%" PRId64, obj->v.i);
	return 0;
}

static int
pml_dump_buf(struct pml_obj *obj, struct pml_obj *dst)
{
	size_t rlen = obj->v.b->size + 3;
	if (pml_obj_checksize(dst, rlen))
		return -ENOMEM;
	dst->offset += snprintf((char *)dst->v.b->data + dst->offset, dst->length - dst->offset,
			"\"%.*s\"", obj->v.b->size, obj->v.b->data);
	return 0;
}

static int
pml_dump_key(struct pml_obj *obj, struct pml_obj *dst)
{
	assert(obj->type == PML_INT);
	if (obj->type != PML_INT)
		return -EINVAL;
	pml_dump_chr('"', dst);
	pml_dump_int(obj, dst);
	pml_dump_chr('"', dst);
	return 0;
}
static int pml_dump_obj(struct pml_obj *obj, struct pml_obj *dst, struct pml_map *ref);
static int
pml_dump_map(struct pml_obj *obj, struct pml_obj *dst, struct pml_map *ref)
{
	int err = 0;
	struct pml_obj key = pml_int(obj->v.i);
	struct pml_obj *res = pml_map_get(ref, &key, PML_INT);
	if (res->v.i++ != 0) {
		if (!(err = pml_obj_checksize(dst, 6)))
			dst->offset += snprintf((char *)dst->v.b->data + dst->offset, dst->length - dst->offset, "\"...\"");
	} else {
		const uint32_t time = T(0);
		uint32_t i, n = obj->v.m->size;
		pml_dump_chr('{', dst);
		for (i = 0; n; i++) {
			if (KEY(obj->v.m->slots, i).type <= PML_EMPTY) continue;
			if (pml_map_expire(obj->v.m, i, time)) { --n; continue; }
			if ((err = pml_dump_key(&KEY(obj->v.m->slots, i), dst))) break;
			if ((err = pml_dump_chr(':', dst))) break;
			if ((err = pml_dump_obj(&VAL(obj->v.m->slots, i), dst, ref))) break;
			if (--n) pml_dump_chr(',', dst);
		}
		pml_dump_chr('}', dst);
	}
	return err;
}

static int
pml_dump_obj(struct pml_obj *obj, struct pml_obj *dst, struct pml_map *ref)
{
	switch (obj->type) {
	case PML_INT: return pml_dump_int(obj, dst);
	case PML_BUF: return pml_dump_buf(obj, dst);
	case PML_MAP: return pml_dump_map(obj, dst, ref);
	default: return -EINVAL;
	}
}

static int
pml_serialize(struct pml_obj *obj, char **out)
{
	struct pml_map *ref = pml_map(8);
	struct pml_buf buf = {0};
	struct pml_obj dst = pml_buf_ref(&buf, NULL, 0);
	int err = pml_dump_obj(obj, &dst, ref); pml_map_release(ref);
	if (!err) {
		dst.v.b->data[dst.offset] = '\0';
		err = dst.offset;
		*out = (char *)dst.v.b->data;
		dst.v.b->data = 0;
		dst.v.b->size = 0;
	}
	return err;
}

#define ADVANCE(obj) \
	if ((obj)->offset++ >= (obj)->length) \
		return -EINVAL;

#define EXPECT(obj, chr) \
	if (next((obj)) != (chr)) \
		return -EINVAL; \
	ADVANCE(obj)

static int
next(struct pml_obj *src)
{
	struct pml_buf *buf = src->v.b;
	while (isspace(buf->data[src->offset]))
		src->offset++;
	return buf->data[src->offset];
}

static int pml_load_obj(struct pml_obj *src, struct pml_obj *dst);

static int
pml_load_int(struct pml_obj *src, struct pml_obj *dst)
{
	int64_t i = 0;
	struct pml_buf *buf = src->v.b;
	while (isdigit(buf->data[src->offset]))
		i *= 10, i += (buf->data[src->offset++]) - '0';
	*dst = pml_int(i);
	return 0;
}

static int
pml_load_buf(struct pml_obj *src, struct pml_obj *dst)
{
	struct pml_buf *buf = src->v.b;
	uint16_t length = 0;
	while (src->offset++ < src->length) {
		if (buf->data[src->offset] == '"') {
			*dst = pml_buf_dup(&buf->data[src->offset - length], length);
			ADVANCE(src);
			return 0;
		}
		length++;
	}
	return -EINVAL;
}

static int
pml_load_key(struct pml_obj *src, struct pml_obj *dst)
{
	struct pml_buf *buf = src->v.b;
	uint16_t length = 0;
	while (src->offset++ < src->length) {
		if (buf->data[src->offset] == '"') {
			*dst = pml_int(strtoll((const char *)&buf->data[src->offset - length], NULL, 10));
			ADVANCE(src);
			return 0;
		} else if (buf->data[src->offset] < '0' || buf->data[src->offset] > '9') {
			break;
		}
		length++;
	}
	return -EINVAL;
}

static int
pml_load_kvp(struct pml_obj *src, struct pml_obj *dst)
{
	struct pml_obj key = {0}, val = {0};
	if (pml_load_key(src, &key))
		return -EINVAL;
	EXPECT(src, ':');
	if (pml_load_obj(src, &val))
		return -EINVAL;
	pml_map_set(dst->v.m, &key, &val);
	pml_obj_release(&key);
	pml_obj_release(&val);
	return 0;
}

static int
pml_load_map(struct pml_obj *src, struct pml_obj *dst)
{
	dst->type = PML_MAP;
	dst->v.m = pml_map(16);
	EXPECT(src, '{');
	for (;;) {
		int err;
		if ((err = pml_load_kvp(src, dst)))
			return err;
		if (next(src) != ',')
			break;
		EXPECT(src, ',');
	}
	EXPECT(src, '}');
	return 0;
}

static int
pml_load_obj(struct pml_obj *src, struct pml_obj *dst)
{
	switch (next(src)) {
	case '{': return pml_load_map(src, dst); break;
	case '"': return pml_load_buf(src, dst); break;
	default:  return pml_load_int(src, dst); break;
	}
}

int pml_deserialize(const char *buf, uint32_t len, struct pml_obj *dst)
{
	struct pml_buf tmp = {0};
	struct pml_obj src = pml_buf_ref(&tmp, (const uint8_t *)buf, len);
	return pml_load_obj(&src, dst);
}

int (*pml_replication_handler)(const char *data, uint32_t len) = NULL;

int
pml_sync(struct pml *pml, const char *data, uint32_t len)
{
	struct pml_obj val = {0};

	pml_deserialize(data, len, &val);
	if (val.v.m->size > 1) {
		pml_map_path_set(pml->map, VAL(val.v.m->slots, 0).v.m, &VAL(val.v.m->slots, 1));
	} else {
		pml_map_path_del(pml->map, VAL(val.v.m->slots, 0).v.m);
	}

	pml_obj_release(&val);
	return 0;
}

#define PATH_DEPTH_MAX (16)
#define REPLICATE_CAP_MAX (2)

static void
path_add_entry(struct pml_obj *slots, int depth, uint32_t value)
{
	VAL(slots, depth) = pml_int(value);
}

static int
pml_replicate(struct pml_map *m, struct pml_obj *key, struct pml_obj *val)
{
	uint32_t i;
	char *buf = 0;
	int len, depth = PATH_DEPTH_MAX;
	struct pml_obj obj_slots[REPLICATE_CAP_MAX * 2] = {0};
	struct pml_obj path_slots[PATH_DEPTH_MAX * 2] = {0};
	struct pml_obj replication_obj = {
		.type = PML_MAP,
		.v.m = &(struct pml_map){
			.ref = 0,
			.size = 0,
			.cap = REPLICATE_CAP_MAX,
			.index = 0,
			.slots = obj_slots,
			.parent = NULL
		}
	};

	if (!pml_replication_handler)
		return 0;

	/* Build key path */
	for (;;) {
		path_add_entry(path_slots, --depth, key->v.i);
		if (!m->parent)
			break;
		key = &KEY(m->parent->slots, m->index);
		m = m->parent;
	}

	/* Set the path in the replication object */
	struct pml_obj path_key = pml_int(0);
	struct pml_obj path = {
		.type = PML_MAP,
		.v.m = &(struct pml_map){
			.size = PATH_DEPTH_MAX - depth,
			.cap = PATH_DEPTH_MAX - depth,
			.slots = &path_slots[depth * 2]
		}
	};

	/* Fix the keys, which were reversed. */
	for (i = 0; i < path.v.m->size; i++) {
		KEY(path.v.m->slots, i) = pml_int(i);
	}

	pml_map_set(replication_obj.v.m, &path_key, &path);

	/* Set the value that requires replication */
	if (val->type != PML_DELETED) {
		struct pml_obj key = pml_int(1);
		pml_map_set(replication_obj.v.m, &key, val);
		pml_obj_release(val);
	}

	/* Serialize */
	len = pml_serialize(&replication_obj, &buf);
	/* Publish */
	if (len > 0) pml_replication_handler(buf, len);
	/* Free */
	free(buf);
	/* Return count */
	return len > 1;
}

/* pml_vm_env()
 *
 * Returns the global environment from the pml_context.
 */
uint64_t
pml_vm_env(uint64_t r1, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(r1, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	return (uintptr_t)pml->map;
}

#define keyindexof(map, val) \
	(((val) - 1) - (map)->slots) >> 1

/*
 * pml_vm_env_get()
 *
 * @k is an object key (its global id/index/offset)
 * @t is the type of value (PML_NUM, PML_BUF, or PML_MAP)
 *
 * Returns the value found (or created)
 */
uint64_t
pml_vm_env_get(uint64_t k, uint64_t t, uint64_t shared, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(k, t, shared, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_obj key = pml_int(k);
	/* This emplace with PML_INT is intentional even though it may be the wrong type
	 * for insertion. PML_INT is a value and therefore requires no runtime allocation
	 * or reference count like objects do. This is the cheapest 'insert if' method,
	 * optimized for lookups and PML_INT values */
	struct pml_obj *r, val = pml_int(0);
	/* If the operation results in an insertion but the target type was not a PML_INT,
	 * allocate the object */
	uint8_t modified = pml_map_try_emplace(pml->map, &key, &val, &r);
	if (modified && t != PML_INT) {
		r->type = t;
		if (t == PML_BUF)
			r->v.b = pml_buf();
		else {
			r->v.m = pml_map(8);
		}
	}
	if (shared) {
		if (r->type == PML_MAP && r->v.m->index == 0) {
			r->v.m->index = keyindexof(pml->map, r);
			r->v.m->parent = pml->map;
		}
		if (modified)
			pml->thr->stats.events += pml_replicate(pml->map, &key, r);
	}
	return r->v.i;
}

/*
 * pml_vm_env_set()
 *
 * @k is an object key (its global id/index/offset)
 * @v is the raw value 
 * @t is the type of value (PML_NUM, PML_BUF, or PML_MAP)
 *
 * Note: pml_vm_obj_xxx - the map is implicitly pml->map
 */
uint64_t
pml_vm_env_set(uint64_t k, uint64_t v, uint64_t t, uint64_t shared, uint64_t ctx, void *arg)
{
	PML_VM_TRACE_CALL(k, v, t, shared, ctx, arg);
	struct pml *pml = arg;
	struct pml_obj key = pml_int(k);
	struct pml_obj val = { .type = t, .expires = 0, .v.i = v };
	struct pml_obj *w, *r = pml_map_set(pml->map, &key, &val);

	/* if @shared is set, the key corresponds to a member that was declared shared */
	if (shared) {
		if (r->type == PML_MAP && r->v.m->index == 0) {
			r->v.m->index = keyindexof(pml->map, r);
			r->v.m->parent = pml->map;
		}
		pml->thr->stats.events += pml_replicate(pml->map, &key, r);
	}

	/* Watcher
	 *
	 * Need to guard during init since sub may be NULL
	 */
	if (pml->thr->sub) {
		w = pml_map_get(pml->thr->sub, &key, PML_EMPTY);
		if (w) {
			int len;
			char *p = 0;
			pml_watcher cb = w->v.p;
			switch ((int)r->type) {
			case WATCH_INT:
				cb((void *)ctx, k, r->type, 8, &r->v.i);
				break;
			case WATCH_STR:
				cb((void *)ctx, k, r->type, r->v.b->size, r->v.b->data);
				break;
			case WATCH_OBJ:
				len = pml_serialize(r, &p);
				if (len > 0)
					cb((void *)ctx, k, r->type, len, p);
				free(p);
				break;
			}
		}
	}

	return r->v.i;
}

/* pml_vm_env_del()
 *
 * Deletes the value in @m at @k
 *
 * Returns the number of keys deleted (0 or 1).
 */
uint64_t
pml_vm_env_del(uint64_t k, uint64_t shared, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(k, shared, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_obj key = pml_int(k);
	struct pml_obj nil = pml_obj(PML_DELETED);
	int r = pml_map_del(pml->map, &key);
	if (shared)
		pml->thr->stats.events += pml_replicate(pml->map, &key, &nil);
	return r;
}

/* pml_vm_int_str()
 *
 * Returns the string representation of the integer eg. 42 -> "42".
 */
uint64_t
pml_vm_int_str(uint64_t num, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(num, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	uint32_t len = snprintf(NULL, 0, "%zd", num);
	struct pml_obj val = pml_buf_dup(NULL, len + 1);
	pml_gc_collect(pml->gc, &val);
	snprintf((char *)val.v.b->data, val.v.b->size, "%zd", num);
	/* remove the null-terminator */
	val.v.b->size = len;
	return val.v.i;
}

/* pml_vm_int_bin()
 *
 * Convert an integer to a fixed width binary string
 */
uint64_t
pml_vm_int_bin(uint64_t i, uint64_t size, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(i, size, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_obj buf = pml_buf_dup(NULL, size);

	assert(size > 0);
	while (size > 0) {
		buf.v.b->data[--size] = (i & 0xff);
		i >>= 8;
	}
	pml_gc_collect(pml->gc, &buf);
	return buf.v.i;
}

/* pml_vm_int_hex()
 *
 * Convert an integer to a fixed width hex string
 */
uint64_t
pml_vm_int_hex(uint64_t i, uint64_t size, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(i, size, r3, r4, r5, arg);
	static const char table[] = "0123456789abcdef";
	struct pml *pml = arg;
	struct pml_obj buf = pml_buf_dup(NULL, size *= 4);

	assert(size > 0);
	while (size > 0) {
		uint8_t byte = i & 0xff;
		uint8_t hi = byte >> 4;
		uint8_t lo = byte & 0xf;
		buf.v.b->data[--size] = table[lo];
		buf.v.b->data[--size] = table[hi];
		buf.v.b->data[--size] = 'x';
		buf.v.b->data[--size] = '\\';
		i >>= 8;
	}
	pml_gc_collect(pml->gc, &buf);
	return buf.v.i;
}

/* pml_vm_str_int()
 *
 * Returns the integer representation of the string eg. "42" -> 42.
 */
uint64_t
pml_vm_str_int(uint64_t b, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(b, r2, r3, r4, r5, arg);
	struct pml_buf *buf = (struct pml_buf *)(uintptr_t)b;
	int64_t val = 0;
	if (buf->size) {
		const char *s = (char *)buf->data, *end = s + buf->size;
		int sign = *s == '-' ? (s++, -1) : 1;
		int base = (buf->size > 2 && s[0] == '0' && (s[1] == 'x' || s[1] == 'X')) ? (s+=2, 16) : 10;
		while (s < end) {
			char ch = *s++;
			if (ch >= '0' && ch <= '9')
				ch -= '0';
			else if (ch >= 'A' && ch < 'A' + base - 10)
				ch -= 'A' - 10;
			else if (ch >= 'a' && ch < 'a' + base - 10)
				ch -= 'a' - 10;
			else
				break;
			val *= base;
			val += ch;
		}
		val *= sign;
	}
	return val;
}

/* pml_vm_str_join()
 *
 * Joins a set of strings separated by s.
 *
 * Returns the new joined string.
 */
uint64_t
pml_vm_str_join(uint64_t s, uint64_t m, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(s, m, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_buf *sep = (struct pml_buf *)(uintptr_t)s;
	struct pml_map *map = (struct pml_map *)(uintptr_t)m;
	uint32_t i, n = map->size, len = sep->size * (n - 1), off = 0;

	for (i = 0, n = map->size; n; i++) {
		if (KEY(map->slots, i).type < PML_INT)
			continue;
		assert(VAL(map->slots, i).type == PML_BUF);
		len += VAL(map->slots, i).v.b->size;
		n--;
	}

	struct pml_obj res = pml_buf_dup(NULL, len);
	for (i = 0, n = map->size; n; i++) {
		struct pml_obj *next = &VAL(map->slots, i);
		memcpy(res.v.b->data + off, next->v.b->data, next->v.b->size);
		off += next->v.b->size;
		if (--n) {
			memcpy(res.v.b->data + off, sep->data, sep->size);
			off += sep->size;
		}
	}
	pml_gc_collect(pml->gc, &res);
	return res.v.i;
}

/* pml_vm_str_split()
 *
 * Splits a string according to a delimiter.
 *
 * Returns a map of splits.
 */
uint64_t
pml_vm_str_split(uint64_t s, uint64_t d, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(s, d, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_buf *str = (struct pml_buf *)(uintptr_t)s;
	const struct pml_buf *sep = (struct pml_buf *)(uintptr_t)d;
	struct pml_map *map = pml_map(str->size / sep->size);
	struct pml_obj key, val, res = pml_obj(PML_MAP); res.v.m = map;
	char *np, *pp = (char *)str->data;
	size_t sz = str->size;
	np = memmem(pp, sz, sep->data, sep->size);
	if (!np) {
		key = pml_int(map->size);
		val = pml_buf_dup((uint8_t *)pp, sz);
		pml_map_set(map, &key, &val);
		pml_obj_release(&val);
	} else {
		do {
			key = pml_int(map->size);
			val = pml_buf_dup((uint8_t *)pp, np - pp);
			pml_map_set(map, &key, &val);
			pml_obj_release(&val);

			sz -= (np - pp + sep->size);
			pp = np + sep->size;
			np = memmem(pp, sz, sep->data, sep->size);
		} while(np);

		key = pml_int(map->size);
		val = pml_buf_dup((uint8_t *)pp, sz);
		pml_map_set(map, &key, &val);
		pml_obj_release(&val);
	}
	pml_gc_collect(pml->gc, &res);
	return res.v.i;
}

/* pml_vm_cap_add()
 *
 * Capture the next byte from the stream.
 */
uint64_t
pml_vm_cap_add(uint64_t id, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(id, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	/* for multi-part */
	struct pml_map *map = 0;
	/* create key */
	struct pml_obj *res, key = pml_int(id);
	/* try emplace with the current buffer */
	uint8_t first = pml_map_try_emplace(pml->map, &key, &pml->buf, &res);

	/* multi-part capture requires a second lookup */
	if (res->type == PML_MAP) {
		struct pml_obj key = pml_int(res->v.m->size-1);
		/* remember the map */
		map = res->v.m;
		/* lookup the last buffer */
		first = pml_map_try_emplace(map, &key, &pml->buf, &res);
	}
	/* check if the last buffer matches the context buffer */
	if (res->v.b != pml->buf.v.b) {
		struct pml_obj seq;
		const uint8_t continuation = (!pml->rscan) ? (pml->buf.offset == 1) : (pml->buf.offset == -1);
		/* gap detected: discard all existing buffers */
		if (!continuation) {
			pml_map_del(pml->map, &key);
			map = pml->map;
			seq = key;
		} else if (!pml->rscan && (((uint32_t)res->offset + res->length) != res->v.b->size)) {
			pml_map_del(pml->map, &key);
			map = pml->map;
			seq = key;
		} else if (pml->rscan && (res->offset + res->length + 1) != 0) {
			pml_map_del(pml->map, &key);
			map = pml->map;
			seq = key;
		/* no gap: setup multi-part map */
		} else if (!map) {
			struct pml_obj val = pml_obj_new(PML_MAP);
			seq = pml_int(0); pml_map_set(val.v.m, &seq, res);
			seq = pml_int(1);
			map = pml_map_set(pml->map, &key, &val)->v.m;
			pml_obj_release(&val);
		/* no grap: already multi-part */
		} else {
			seq = pml_int(map->size);
		}
		first = pml_map_try_emplace(map, &seq, &pml->buf, &res);
	}
	/* detect bug in above logic */
	assert(res->v.b == pml->buf.v.b);

	/* offset(pml) should always 1 byte ahead in a left to right
	 * scan and always 1 byte behind us in a right to left scan */
	if (!pml->rscan) {
		if (first) {
			res->offset = pml->buf.offset - 1;
			res->length = 0;
		} else if (res->offset + res->length != pml->buf.offset - 1) {
			res->offset = pml->buf.offset - 1;
			res->length = 0;
		}
	} else {
		if (first) {
			res->offset = pml->buf.offset;
			res->length = 0;
		} else if (res->offset != pml->buf.offset) {
			res->offset = pml->buf.offset;
			res->length = 0;
		}
		res->offset--;
	}
	res->length++;
	return 0;
}

/* pml_vm_cap_get()
 *
 * Get a capture buffer
 *
 * The compiler generates instructions in transitions to handle capture groups. We need to be able to handle
 * when captures span packet boundaries due to normal segmentation, but we also want to optimize for when
 * the capture is wholly resident in the current buffer. To make matter slightly more challenging, it is both
 * possible and common that multiple buffers are active at the same time and overlap (i.e. some or all parts of
 * one capture group is in common with one or more other capture groups).
 *
 * To attack this problem, we generate a unique id to represent a discrete capture group. When a transition
 * generates code to capture, it provide this id to the capturing process. If no buffer is allocated for the
 * capture id, the capturing process allocates state for the buffer, recording the starting offset and its
 * length. This process repeats for each offset that belongs with the buffer id.
 *
 * If during capture a gap is the capture is detected, the process truncates the buffer by overwriting the
 * offset and length. At the end of the execution, any incomplete buffers are discarded.
 *
 * During the capturing process, a buffer is represented by a 16-bit offset and a 16-bit length. Each buffer
 * representation is mapped by its identifier and stored in a sparse array. A buffer will not fully materialize
 * until it is requested in code, thus no copy occurs. Zero copy access to regions within the current packet
 * should be possible, but buffers that span packets will require copies.
 */
uint64_t
pml_vm_cap_get(uint64_t id, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(id, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_obj key = pml_int(id);
	struct pml_obj *res = pml_map_get(pml->map, &key, PML_BUF);

	/* check if this is a multi-part capture */
	if (res->type == PML_MAP) {
		struct pml_obj val;
		struct pml_map *map = res->v.m;
		uint32_t i, len = 0, off = 0;
		/* compute combined length */
		for (i = 0; i < map->size; i++) {
			len += VAL(map->slots, i).length;
		}
		/* combine buffers */
		val = pml_buf_dup(NULL, len);
		for (i = 0; i < map->size; i++) {
			struct pml_obj *next = &VAL(map->slots, i);
			if (!pml->rscan) {
				memcpy(val.v.b->data + off, next->v.b->data + next->offset, next->length);
				off += next->length;
			} else {
				uint16_t offset = next->length + next->offset + 1;
				memcpy(val.v.b->data + len - next->length, next->v.b->data + offset, next->length);
				len -= next->length;
			}
		}
		/* discard chain */
		pml_map_del(pml->map, &key);
		assert(val.v.b->ref == 1);
		pml_gc_collect(pml->gc, &val);
		return val.v.i;
	}

	/* capture entirely in current packet (zero-copy) */
	const int16_t off = !pml->rscan ? res->offset : pml->buf.length + pml->buf.offset;
	struct pml_obj val = pml_buf_dup(pml->buf.v.b->data + off, res->length);
	/* discard buffer */
	pml_map_del(pml->map, &key);
	assert(val.v.b->ref == 1);
	pml_gc_collect(pml->gc, &val);
	return val.v.i;
}

/* pml_vm_str_hex()
 *
 * Convert a string to its hex representation
 */
uint64_t
pml_vm_str_hex(uint64_t str, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(str, r2, r3, r4, r5, arg);
	static const char table[] = "0123456789abcdef";
	struct pml *pml = arg;
	struct pml_buf *b = (struct pml_buf *)(uintptr_t)str;
	/* 2 bytes per char + 2 bytes for "0x" prefix */
	size_t size = b->size ? (b->size * 2) + 2 : 0;
	struct pml_obj buf = pml_buf_dup(NULL, size);
	pml_gc_collect(pml->gc, &buf);

	if (size) {
		buf.v.b->data[0] = '0';
		buf.v.b->data[1] = 'x';
		size -= 2;
		for (size_t i = 0, off = 2; i < b->size; i++) {
			uint8_t byte = b->data[i] & 0xff;
			uint8_t hi = byte >> 4;
			uint8_t lo = byte & 0xf;
			buf.v.b->data[off++] = table[hi];
			buf.v.b->data[off++] = table[lo];
		}
	}
	return buf.v.i;
}

/* pml_vm_bin_int()
 *
 * Convert a binary string to int representation
 */
uint64_t
pml_vm_bin_int(uint64_t bin, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(bin, r2, r3, r4, r5, arg);
	int64_t val = 0;
	uint32_t i;
	struct pml_buf *b = (struct pml_buf *)(uintptr_t)bin;
	for (i = 0; i < b->size; i++) {
		val *= 256;
		val += b->data[i];
	}
	return val;
}

/* pml_vm_buf_new()
 *
 * Returns the string representation of the packed integer eg. 0x616263 -> 'abc'
 */
uint64_t
pml_vm_buf_new(uint64_t v, uint64_t size, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(v, size, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_obj obj = pml_buf_dup(NULL, size);
	uint8_t i = 0;
	for (i = 0; i < size; i++) {
		obj.v.b->data[i] = v & 0xff;
		v = v >> 8;
	}
	obj.v.b->size = i;
	pml_gc_collect(pml->gc, &obj);
	return obj.v.i;
}

/* pml_vm_buf_cmp()
 *
 * Compare the strings a and b.
 *
 * Returns an integer less than, equal to, or greater than zero if the first n bytes of
 * a is found to be less than, to match, or be greater than the first n bytes of b.
 */
uint64_t
pml_vm_buf_cmp(uint64_t a, uint64_t b, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(a, b, r3, r4, r5, arg);
	struct pml_buf *buf_a = (struct pml_buf *)(uintptr_t)a;
	struct pml_buf *buf_b = (struct pml_buf *)(uintptr_t)b;

	if (buf_a->size == buf_b->size) {
		return memcmp(buf_a->data, buf_b->data, buf_a->size);
	}
	return buf_a->size < buf_b->size ? -1 : 1;
}

/* pml_vm_buf_cat()
 *
 * Concatenate the strings
 *
 * Returns the new concatenated string
 */
uint64_t
pml_vm_buf_cat(uint64_t a, uint64_t b, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(a, b, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_obj res = pml_buf_cat((struct pml_buf *)(uintptr_t)a, (struct pml_buf *)(uintptr_t)b);
	pml_gc_collect(pml->gc, &res);
	return res.v.i;
}

/* pml_vm_buf_len()
 *
 * Returns the length of the buffer
 */
uint64_t
pml_vm_buf_len(uint64_t b, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(b, r2, r3, r4, r5, arg);
	struct pml_buf *buf = (struct pml_buf *)(uintptr_t)b;
	return buf->size;
}

/* pml_vm_map_new()
 *
 */
uint64_t
pml_vm_map_new(uint64_t type, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(type, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_obj obj = pml_obj_new(type);
	assert(type == PML_MAP);
	pml_gc_collect(pml->gc, &obj);
	return obj.v.i;
}

/* pml_vm_map_try()
 *
 * Returns the value in @m stored at @k. Returns an empty value of type @t.
 *
 * This does not modify the map.
 */
uint64_t
pml_vm_map_try(uint64_t m, uint64_t k, uint64_t t, uint64_t e, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(m, k, t, e, r5, arg);
	const uint32_t expires = e ? T(e) : 0;
	struct pml *pml = arg;
	struct pml_map *map = (struct pml_map *)(uintptr_t)m;
	struct pml_obj val, key = pml_int(k);
	struct pml_obj *res = pml_map_get(map, &key, PML_EMPTY);
	if (!res) {
		val = pml_obj_new(t);
		pml_gc_collect(pml->gc, &val);
		res = &val;
	} else if (expires) {
		res->expires = expires;
	}
	return res->v.i;
}

/* pml_vm_map_get()
 *
 * Returns the value in @m stored at @k, optionally inserting a default value of type @t.
 */
uint64_t
pml_vm_map_get(uint64_t m, uint64_t k, uint64_t t, uint64_t e, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(m, k, t, e, r5, arg);
	const uint32_t expires = e ? T(e) : 0;
	struct pml_map *map = (struct pml_map *)(uintptr_t)m;
	struct pml_obj key = pml_int(k);
	struct pml_obj *res = pml_map_get(map, &key, t);
	if (expires)
		res->expires = expires;
	return res->v.i;
}

/* pml_vm_map_set()
 *
 * Stores the value @v in @m at @k
 *
 * Returns v.
 */
uint64_t
pml_vm_map_set(uint64_t m, uint64_t k, uint64_t v, uint64_t t, uint64_t e, void *arg)
{
	PML_VM_TRACE_CALL(m, k, v, t, e, arg);
	struct pml *pml = arg;
	const uint32_t expires = e ? T(e) : 0;
	struct pml_map *map = (struct pml_map *)(uintptr_t)m;
	struct pml_obj key = pml_int(k);
	struct pml_obj val = { .type = t, .expires = expires, .v.i = v };
	pml_map_set(map, &key, &val);
	if (map->index)
		pml->thr->stats.events += pml_replicate(map, &key, &val);
	return v;
}

/* pml_vm_map_del()
 *
 * Deletes the value in @m at @k
 *
 * Returns the number of keys deleted (0 or 1).
 */
uint64_t
pml_vm_map_del(uint64_t m, uint64_t k, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(m, k, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_map *map = (struct pml_map *)(uintptr_t)m;
	struct pml_obj key = pml_int(k);
	struct pml_obj nil = pml_obj(PML_DELETED);
	int r = pml_map_del(map, &key);
	if (r && map->index)
		pml->thr->stats.events += pml_replicate(map, &key, &nil);
	return r;
}

/* pml_vm_map_str()
 *
 * Stringify a map.
 *
 * Returns the string representation of the map.
 */
uint64_t
pml_vm_map_str(uint64_t m, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(m, r2, r3, r4, r5, arg);
	struct pml_map *ref = pml_map(8);
	struct pml *pml = arg;
	struct pml_obj obj = pml_obj(PML_MAP); obj.v.m = (struct pml_map *)(uintptr_t)m;
	struct pml_obj dst = pml_buf_ref(NULL, NULL, 0);
	pml_dump_map(&obj, &dst, ref);
	pml_map_release(ref);
	pml_gc_collect(pml->gc, &dst);
	dst.v.b->size = dst.offset;
	return dst.v.i;
}

/* pml_vm_map_len()
 *
 * Returns the number of elements stored in the map
 */
uint64_t
pml_vm_map_len(uint64_t b, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(b, r2, r3, r4, r5, arg);
	struct pml_map *map = (struct pml_map *)(uintptr_t)b;
	return map->size;
}

/*
 * get_peek_offset()
 *
 * off is where you are wanting to position the cursor relative to the current offset
 * len is how many bytes you want after the new cursor position
 *
 * Returns the new offset (absolute). If the in parameter @len was set greater than
 * the number of bytes available after the reposition, @len is truncated.
 */
static int16_t
get_peek_offset(const struct pml *pml, int16_t off, uint64_t *len)
{
	int16_t available;
	int16_t new_offset = pml->buf.offset + off;
	if (new_offset > pml->buf.length) {
		new_offset = pml->buf.length;
	} else if (new_offset < 0) {
		new_offset = 0;
	}
	available = pml->buf.length - new_offset;
	if (*len > (uint64_t)available)
		*len = available;
	return new_offset;
}

/* pml_vm_peek()
 *
 * Peek at the buffer under inspection
 */
uint64_t
pml_vm_peek(uint64_t off, uint64_t len, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(off, len, r3, r4, r5, arg);
	struct pml_obj buf;
	struct pml *pml = arg;
	off = get_peek_offset(pml, off, &len);
	buf = pml_buf_dup(pml->buf.v.b->data + off, len);
	pml_gc_collect(pml->gc, &buf);
	return buf.v.i;
}

/* pml_vm_remaining()
 *
 * Get the number of bytes remaining in the buffer
 */
uint64_t
pml_vm_remaining(uint64_t r1, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(r1, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	return remaining(pml);
}

/* pml_vm_offset()
 *
 * Get the offset into the current buffer
 */
uint64_t
pml_vm_offset(uint64_t r1, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(r1, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	return offset(pml);
}

/* pml_vm_yank()
 *
 * Yanks @len bytes from the buffer under inspection
 */
uint64_t
pml_vm_yank(uint64_t len, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(len, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_buf *b = (struct pml_buf *)pml_vm_peek(0, len, r3, r4, r5, arg);
	pml->buf.offset += b->size;
	return (uintptr_t)b;
}

/* pml_vm_skip()
 *
 * Seek forward or backward len bytes in the buffer under inspection
 */
uint64_t
pml_vm_skip(uint64_t len, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(len, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	int64_t offset = pml->buf.offset;
	uint64_t unused = 0;
	pml->buf.offset = get_peek_offset(pml, len, &unused);
	return pml->buf.offset - offset;
}

/* pml_vm_bstoh()
 *
 * Convert a big-endian string to a host order integer
 */
uint64_t
pml_vm_bstoh(uint64_t s, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(s, r2, r3, r4, r5, arg);
	uint64_t val = 0;
	struct pml_buf *buf = (struct pml_buf *)(uintptr_t)s;
	for (uint32_t i = 0; i < buf->size; i++) {
		val = (val << 8) | (buf->data[i]);
	}
	return val;
}

/* pml_vm_lstoh()
 *
 * Convert a little-endian string to a host order integer
 */
uint64_t
pml_vm_lstoh(uint64_t s, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(s, r2, r3, r4, r5, arg);
	uint64_t val = 0;
	struct pml_buf *buf = (struct pml_buf *)(uintptr_t)s;
	for (uint32_t i = buf->size; i >= 1; i--) {
		val = (val << 8) | (buf->data[i-1]);
	}
	return val;
}

/* pml_vm_htobs()
 *
 * Convert a host order integer to a binary string;
 */
uint64_t
pml_vm_htobs(uint64_t i, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(i, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	uint32_t size = 8 - (__builtin_clzll(i | 1) >> 3);
	struct pml_obj buf = pml_buf_dup(NULL, size);

	assert(size > 0);
	while (size > 0) {
		buf.v.b->data[--size] = (i & 0xff);
		i >>= 8;
	}
	pml_gc_collect(pml->gc, &buf);
	return buf.v.i;
}

/* pml_vm_macaddr()
 *
 * Convert an integer mac address to a string.
 */
uint64_t
pml_vm_macaddr(uint64_t i, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(i, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	static const size_t size = sizeof("00:00:00:00:00:00") + 1;
	struct pml_obj buf = pml_buf_dup(NULL, size);

	snprintf((char *)buf.v.b->data, buf.v.b->size, "%02x:%02x:%02x:%02x:%02x:%02x",
			(unsigned)(i >> 40) & 0xff,
			(unsigned)(i >> 32) & 0xff,
			(unsigned)(i >> 24) & 0xff,
			(unsigned)(i >> 16) & 0xff,
			(unsigned)(i >> 8) & 0xff,
			(unsigned)i & 0xff
	);
	pml_gc_collect(pml->gc, &buf);
	buf.v.b->size = size - 1;
	return buf.v.i;
}

/* pml_vm_pton()
 *
 * Convert an IP address from text to binary
 */
uint64_t
pml_vm_pton(uint64_t addr, uint64_t af, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(addr, af, r3, r4, r5, arg);
	struct pml *pml = arg;
	size_t size = af == 0 ? 4 : 16;
	int family = af == 0 ? AF_INET : AF_INET6;
	struct pml_buf *b = (struct pml_buf *)(uintptr_t)addr;
	struct pml_obj buf = pml_buf_dup(NULL, size);
	char scratch[INET6_ADDRSTRLEN] = {0};

	/* The input string needs to be null terminated, so we must copy it. */
	memcpy(scratch, b->data, b->size);
	int r = inet_pton(family, scratch, buf.v.b->data);
	assert(r == 1);
	pml_gc_collect(pml->gc, &buf);
	return buf.v.i;
}

/* pml_vm_ntop()
 *
 * Convert an IP address from binary to text
 */
uint64_t
pml_vm_ntop(uint64_t addr, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(addr, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	struct pml_buf *b = (struct pml_buf *)(uintptr_t)addr;
	int af = b->size == 4 ? AF_INET : AF_INET6;
	struct pml_obj buf = pml_buf_dup(NULL, INET6_ADDRSTRLEN);
	const char *r = inet_ntop(af, b->data, (char *)buf.v.b->data, INET6_ADDRSTRLEN);
	assert(r);
	buf.v.b->size = strlen((char *)buf.v.b->data);
	pml_gc_collect(pml->gc, &buf);
	return buf.v.i;
}

/* pml_vm_print()
 *
 * Print a string to stdout.
 *
 * Returns the number of bytes written.
 */
uint64_t
pml_vm_print(uint64_t b, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(b, r2, r3, r4, r5, arg);
	struct pml_buf *buf = (struct pml_buf *)(uintptr_t)b;
	return printf("%.*s\n", buf->size, buf->data);
}

/* pml_vm_scan()
 *
 * Internal scan.
 *
 * Scan modifies the callers context, but supports optional global environments
 *
 * Returns the next state, or 0 when the scan runs out of states.
 */
static int pml_analyze(struct pml *pml, void *ctx);
static int pml_sendeob(struct pml *pml, void *ctx);

static inline int
pml_unpack_state(struct pml *pml, uint32_t bits)
{
	pml->rscan = bits & 1;
	pml->block = (bits >> 1) & 1;
	pml->resume = bits >> 2;
	return 0;
}

static uint64_t
pml_vm_scan(uint64_t state, uint64_t data, uint64_t lang, uint64_t env, uint64_t ctx, void *arg)
{
	struct pml *pml = arg;
	uint32_t result;
	struct pml_buf *buf = (struct pml_buf *)(uintptr_t)data;
	struct pml_map *map = env ? (struct pml_map *)(uintptr_t)env : pml->map;
	if (!buf->size)
		return state;
	struct pml tmp = {
		.yield = 0,
		.lang = lang,
		.rscan = 0,
		.block = 0,
		.value = 0,
		.resume = 0,
		.buf = {
			.type = PML_BUF,
			.offset = 0,
			.length = buf->size,
			.v.i = data
		},
		.map = map,
		.gc = pml_map(8),
		.thr = pml->thr
	};
	pml_unpack_state(&tmp, state);
	result = pml_analyze(&tmp, (void *)ctx);

	pml->thr->stats.subscans++;
	pml_map_release(tmp.gc);
	return (result << 2) | (state & 0x3);
}

/* pml_vm_shmr()
 *
 * Read data from the pml_context.
 *
 * Returns the bytes read.
 */
uint64_t
pml_vm_shmr(uint64_t off, uint64_t len, uint64_t ctx, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(off, len, ctx, r4, r5, arg);
	struct pml *pml = arg;
	const uint8_t *address = (const uint8_t *)ctx + off;
	struct pml_obj res = pml_buf_dup(address, len);
	pml_gc_collect(pml->gc, &res);
	return res.v.i;
}

/* pml_vm_shmw()
 *
 * Write data into the pml_context.
 *
 * Returns the bytes written.
 */
uint64_t
pml_vm_shmw(uint64_t off, uint64_t len, uint64_t b, uint64_t ctx, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(off, len, b, ctx, r5, arg);
	uint8_t *address = (uint8_t *)ctx + off;
	struct pml_buf *buf = (struct pml_buf *)(uintptr_t)b;
	memcpy(address, buf->data, buf->size);
	return b;
}

/* pml_vm_bytes()
 *
 * Get the number of bytes in the connection
 */
uint64_t
pml_vm_bytes(uint64_t lang, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(lang, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	return pml->bytes[lang];
}

/* pml_vm_packets()
 *
 * Get the number of bytes in the connection
 */
uint64_t
pml_vm_scans(uint64_t lang, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(lang, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	return pml->scans[lang];
}

/* pml_vm_limit()
 *
 * Set the scan limit for a PML. This is number of scans for block more and number of bytes
 * for stream mode.
 */
uint64_t
pml_vm_limit(uint64_t limit, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(limit, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	pml->limit = limit;
	return 0;
}

/* FNV1a 64 bit variant */
static inline uint64_t
fnv1a64(const uint8_t *data, uint32_t len)
{
    uint64_t hash = 14695981039346656037ull;
    for (size_t i = 0; i < len; i++) {
        hash ^= data[i];
        hash *= 1099511628211ull;
    }
    return hash;
}

/* pml_vm_hash()
 *
 * Compute the fnv1a64 hash of s
 */
uint64_t
pml_vm_hash(uint64_t s, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(s, r2, r3, r4, r5, arg);
	struct pml_buf *buf = (struct pml_buf *)s;
	return fnv1a64(buf->data, buf->size);
}

static void
pml_skip_length(struct pml *pml, uint32_t len)
{
	const int32_t pending = remaining(pml) - len;
	if (pending < 0) {
		pml->buf.offset += pending + len;
		pml->value = -pending;
	} else {
		pml->buf.offset += len;
		pml->value -= len;
	}
}

static void
pml_fill_buffer(struct pml *pml, uint32_t len)
{
	assert(remaining(pml));
	struct pml_map *map = 0;
	const int32_t pending = remaining(pml) - len;
	/* using $0 */
	const uint32_t id = 0;
	/* create key */
	struct pml_obj *res, key = pml_int(id);
	/* try emplace with the current buffer */
	uint8_t first = pml_map_try_emplace(pml->map, &key, &pml->buf, &res);

	/* multi-part read requires a second lookup */
	if (res->type == PML_MAP) {
		struct pml_obj key = pml_int(res->v.m->size-1);
		/* remember the map */
		map = res->v.m;
		/* lookup the last buffer */
		first = pml_map_try_emplace(map, &key, &pml->buf, &res);
	}
	/* check if the last buffer matches the context buffer */
	if (res->v.b != pml->buf.v.b) {
		struct pml_obj seq;
		if (!map) {
			struct pml_obj val = pml_obj_new(PML_MAP);
			seq = pml_int(0); pml_map_set(val.v.m, &seq, res);
			seq = pml_int(1);
			map = pml_map_set(pml->map, &key, &val)->v.m;
			pml_obj_release(&val);
		/* already multi-part */
		} else {
			seq = pml_int(map->size);
		}
		first = pml_map_try_emplace(map, &seq, &pml->buf, &res);
	}
	assert(first);

	if (pending < 0) {
		res->length = pending + len;
		pml->buf.offset += pending + len;
		pml->value = -pending;
	} else {
		res->length = len;
		pml->buf.offset += len;
		pml->value -= len;
	}
}

#define PML_MODE_MASK 0x0001

/* pml_vm_yield()
 */
uint64_t
pml_vm_yield(uint64_t len, uint64_t m, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(len, m, r3, r4, r5, arg);
	struct pml *pml = arg;
	uint8_t block = m & PML_MODE_MASK;
	pml->yield = m >> 1;
	if (len > 0) {
		if (block) {
			uint16_t limit = remaining(pml);
			if (len >= limit)
				len = limit;
		}
		pml->value = len;
		pml->thr->stats.yields += pml->yield < 3;
	} else {
		pml->value = 0;
	}
	return 0;
}

uint64_t
pml_vm_advance(uint64_t r1, uint64_t r2, uint64_t r3, uint64_t r4, uint64_t r5, void *arg)
{
	PML_VM_TRACE_CALL(r1, r2, r3, r4, r5, arg);
	struct pml *pml = arg;
	uint16_t value;

	/* The machine did yield but has now resumed. Clear flag and return value */
	if (pml->yield != PML_YIELD_NONE) {
		value = pml->value;
		pml->yield = PML_YIELD_NONE;
		pml->value = 0;
		return value;
	}

	/* Normal data stream processing */
	if (!pml->rscan) {
		value = pml->buf.v.b->data[pml->buf.offset++];
	} else {
		value = pml->buf.v.b->data[pml->buf.length + --pml->buf.offset];
	}
	value += (256 * pml->lang);
	return value;
}

enum pml_entry {
	PML_INIT = 0,
	PML_MAIN = 1,
};

static inline const struct bpf_insn *
ef(const void *start, enum pml_entry entry)
{
	const void *base = (const char *)start + sizeof(uint64_t);
	const uint64_t *offset_map = base;
	const uint64_t offset = offset_map[entry];
	return (const struct bpf_insn *)base + offset;
}

static inline const struct bpf_insn *
tf(const void *start, uint64_t state)
{
	const void *base = (const char *)start + sizeof(uint64_t);
	const uint64_t *offset_map = base;
	const uint64_t offset = offset_map[PML_MAIN + state];
	return (const struct bpf_insn *)base + offset;
}

#define PML_STACK_SIZE (16 * sizeof(uint64_t))

BPF_PROG_RUN(pml_prog_run, PML_STACK_SIZE)

static int
check_version(uint64_t version)
{
	const int32_t major = version >> 48;
	const int32_t minor = (version >> 32) & 0xffff;
	return major == PML_VERSION_MAJOR && minor >= PML_VERSION_MINOR;
}

static struct pml_thr *
pml_thr(const void *bpf_data, uint32_t bpf_size)
{
	struct pml_thr *thr;
	thr = calloc(1, sizeof(*thr));
	if (!thr)
		return NULL;
	thr->ref = 1;
	thr->bpf_data = (void *)bpf_data;
	thr->bpf_size = bpf_size;
	thr->sym = NULL;
	thr->buf = NULL;
	return thr;
}

int
pml_load(struct pml **pmlp, const char *file)
{
	PML_TRACE("pml_load(%s)\n", file);
	struct stat sb;
	struct pml *pml;
	struct pml_map *res;
	struct pml_obj key, *val;
	void *bpf_data;
	uint32_t bpf_size;
	int fd;

	if ((fd = open(file, O_RDONLY)) == -1) {
		return -EBADF;
	}
	if (fstat(fd, &sb) != -1) {
		bpf_size = sb.st_size;
		bpf_data = mmap(NULL, sb.st_size, PROT_READ, MAP_PRIVATE, fd, 0);
	}
	close(fd);
	if (bpf_data == MAP_FAILED) {
		return -EINVAL;
	}

	if (bpf_size < 8 || !check_version(*(uint64_t *)bpf_data)) {
		munmap(bpf_data, bpf_size);
		return -EINVAL;
	}

	pml = calloc(1, sizeof(*pml));
	if (!pml) {
		munmap(bpf_data, bpf_size);
		return -ENOMEM;
	}

	pml->thr = pml_thr(bpf_data, bpf_size);
	pml->map = pml_map(8);
	pml->gc = pml_map(8);

	/* map[int] -> map[int] -> str; */
	res = (struct pml_map *)pml_prog_run(NULL, ef(bpf_data, PML_INIT), pml);
#define PML_STRTAB 0
	key = pml_int(PML_STRTAB);
	val = pml_map_get(res, &key, PML_MAP);
	pml->thr->sym = val->v.m;
	pml_map_acquire(pml->thr->sym);
#define PML_OFFSET 1
	key = pml_int(PML_OFFSET);
	val = pml_map_get(res, &key, PML_MAP);
	pml->thr->mem = val->v.m;
	pml_map_acquire(pml->thr->mem);

	pml->thr->sub = pml_map(pml->thr->mem->size);
	if (!pml->thr->sub)
		return -ENOMEM;

	*pmlp = pml;
	return 0;
}

int
pml_main(struct pml *pml, void *ctx)
{
	uint32_t state = pml_prog_run(ctx, ef(pml->thr->bpf_data, PML_MAIN), pml);
	pml_unpack_state(pml, state);
	return (int)pml->resume;
}

static struct pml_thr *
pml_thr_acquire(struct pml_thr *thr)
{
	++thr->ref;
	return thr;
}

static void
pml_thr_release(struct pml_thr *thr)
{
	if (--thr->ref == 0) {
		if (thr->buf) {
			pml_buf_release(thr->buf);
		}
		pml_map_release(thr->sym);
		pml_map_release(thr->mem);
		pml_map_release(thr->sub);
		if (thr->bpf_data)
			munmap(thr->bpf_data, thr->bpf_size);
		free(thr);
	}
}

int
pml_exit(struct pml *pml)
{
	pml_map_release(pml->map);
	pml_map_release(pml->gc);
	pml_thr_release(pml->thr);
	free(pml);
	return 0;
}

int
pml_exec(struct pml **pmlp, const void *bpf_data, uint32_t bpf_size, uint64_t *res)
{
	struct pml *pml = calloc(1, sizeof(*pml));
	if (pml) {
		pml->thr = pml_thr(bpf_data, bpf_size);
		/* these normally created during load() */
		pml->thr->sym = pml_map(0);
		pml->thr->mem = pml_map(0);
		pml->thr->sub = pml_map(0);

		pml->map = pml_map(8);
		pml->gc = pml_map(8);
		*res = pml_prog_run(NULL, pml->thr->bpf_data, pml);

		pml->thr->bpf_data = NULL;
		pml->thr->bpf_size = 0;

		*pmlp = pml;
		return 0;
	}
	return -ENOMEM;
}

int
pml_clone(struct pml **pmlp, struct pml *parent)
{
	struct pml *pml = calloc(1, sizeof(*pml));
	if (pml) {
		pml->thr = pml_thr_acquire(parent->thr);
		pml->map = pml_map_clone(parent->map);
		pml->map->parent = pml->map;
		pml->gc = pml_map(8);
		pml->resume = 1;
		*pmlp = pml;
		return 0;
	}
	return -ENOMEM;
}

int
pml_nameof(struct pml *pml, uint32_t index, const char **name)
{
	struct pml_obj *res;
	struct pml_obj key = pml_int(index);
	struct pml_map *sym = (struct pml_map *)pml->thr->sym;
	if (index >= sym->size)
		return -ERANGE;
	res = pml_map_get(sym, &key, PML_EMPTY);
	if (!res)
		return -EINVAL;
	*name = (char *)res->v.b->data;
	return res->v.b->size;
}

static struct pml_obj
pml_ptr(void *ptr)
{
	return (struct pml_obj){.type = PML_PTR, .offset = 0, .length = 0, .expires = 0, .v.p = ptr};
}

int
pml_watchpoint(struct pml *pml, const char *name, pml_watcher cb)
{
	struct pml_obj *key, *val, obj = pml_str_dup(name);
	struct pml_map *m =  pml->thr->mem;
	pml_map_for_each_entry(m, key, val) {
		val->length = val->v.b->size;
		if (pml_obj_eq(val, &obj)) {
			struct pml_obj ptr = pml_ptr(cb);
			pml_map_set(pml->thr->sub, key, &ptr);
			pml_obj_release(&obj);
			return key->v.i;
		}
	}
	pml_obj_release(&obj);
	return -EINVAL;
}

static int
pml_analyze(struct pml *pml, void *ctx)
{
	PML_TRACE("pml_analyze(%p)\n", pml);

	if (!remaining(pml) || !pml->resume)
		goto exit;

	if (pml->yield) {
suspended:
		assert(pml->resume);
		assert(pml->yield == PML_YIELD_SKIP || pml->yield == PML_YIELD_YANK);
		if (remaining(pml)) {
			if (yanking(pml))
				pml_fill_buffer(pml, pml->value);
			else
				pml_skip_length(pml, pml->value);
		}

		/* need more data */
		if (pml->value)
			goto exit;

		assert(pml->resume);
		goto resume;
	}
resume:
	pml->resume = pml_prog_run(ctx, tf(pml->thr->bpf_data, pml->resume), pml);
	pml_gc_cleanup(pml->gc);

	if (pml->resume) {
		switch (pml->yield) {
		case PML_YIELD_NONE:
			if (!remaining(pml)) {
				if (pml->block) {
					pml_sendeob(pml, ctx);
				}
				goto exit;
			}
			goto resume;
		case PML_YIELD_SKIP:
		case PML_YIELD_YANK:
			if (pml->value)
				goto suspended;
			goto resume;
		case PML_YIELD_DATA:
			assert(pml->value == 512 || pml->value == 513);
			goto resume;
		}
	}
exit:
	return pml->resume;
}

int
pml_sendeob(struct pml *pml, void *ctx)
{
	pml->yield = PML_YIELD_DATA;
	pml->value = 514;
	pml->resume = pml_prog_run(ctx, tf(pml->thr->bpf_data, pml->resume), pml);
	pml_gc_cleanup(pml->gc);
	return pml->resume;
}

static int
pml_thr_sync(struct pml_thr *thr)
{
	if (thr->buf->ref > 1) {
		struct pml_buf *b = thr->buf;
		void *dst = malloc(b->size);
		if (!dst) {
			return -ENOMEM;
		}
		memcpy(dst, b->data, b->size);
		b->data = dst;
		b->free = 1;

		pml_buf_release(thr->buf);
		thr->buf = NULL;
		thr->stats.syncs++;
	}
	return 0;
}

static int
scans_limit(struct pml *pml)
{
	return (((uint64_t)pml->scans[0] + pml->scans[1]) >= pml->limit);
}

static int
bytes_limit(struct pml *pml)
{
	return (((uint64_t)pml->bytes[0] + pml->bytes[1]) >= pml->limit);
}

int
pml_scan(struct pml *pml, const uint8_t *data, uint32_t len, enum pml_match_lang lang, void *ctx)
{
	struct pml_thr *thr = pml->thr;
	uint32_t entry_state = pml->resume;

	if (!pml)
		return -EINVAL;

	if (!thr->buf) {
		thr->buf = pml_buf();
		if (!thr->buf)
			return -ENOMEM;
	}

	pml->buf = pml_buf_ref(thr->buf, data, len);
	pml->lang = lang;
	pml_analyze(pml, ctx);
	pml->bytes[lang] += len;
	pml->scans[lang] += 1;
	thr->stats.scans++;

	/* TODO:
	 *
	 * We need to figure out what to do about this with an integration. If the
	 * integration is already yanking packets, it would be better to provide
	 * a callback mechanism when we don't need the buffer. For testing, if a
	 * reference is held by the environment, we're just copying the data to make
	 * it resident.
	 *
	 * This should only be happening with captures spanning buffers.
	 *
	 * TODO:
	 *
	 * Block mode should be pushed outward. In block mode, if a buffer does not
	 * materialize, it needs to be discarded - we don't want to hold a reference
	 * to data that will never complete.
	 *
	 */

	if (pml_thr_sync(thr) < 0) {
		pml->resume = 0;
		return -ENOMEM;
	}

	if (pml->limit > 0) {
		if (pml->block) {
			if (scans_limit(pml))
				pml->resume = 0;
			else
				pml->resume = entry_state;
		} else {
			if (bytes_limit(pml))
				pml->resume = 0;
		}
	} else if (pml->block) {
		/* Unlimited scanning has been set for block mode */
		pml->resume = entry_state;
	}

	return (int) pml->resume;
}

int
pml_eval(struct pml *pml, int state, const uint8_t *data, uint32_t len, uint32_t off, void *ctx)
{
	struct pml_thr *thr = pml->thr;

	if (!pml)
		return -EINVAL;

	if (!thr->buf) {
		thr->buf = pml_buf();
		if (!thr->buf)
			return -ENOMEM;
	}

	pml->resume = state;
	pml->buf = pml_buf_ref(thr->buf, data, len);
	pml->buf.offset = off;
	pml->lang = PML_MATCH_NORMAL;
	pml->rscan = 0;
	pml_analyze(pml, ctx);
	thr->stats.evals++;

	if (pml_thr_sync(thr) < 0) {
		pml->resume = 0;
		return -ENOMEM;
	}

	return (int)pml->resume;
}

uint16_t
pml_datetime(uint8_t wday, uint8_t hour, uint8_t min)
{
	return wday * 1440 + hour * 60 + min;
}

int
pml_listen(pml_replication handler)
{
	pml_replication_handler = handler;
	return 0;
}

int
pml_stats(struct pml *pml, struct pml_stats *stats)
{
	struct pml_thr *thr;

	if (!pml || !stats)
		return -EINVAL;

	thr = pml->thr;
	stats->syncs += thr->stats.syncs; thr->stats.syncs = 0;
	stats->scans += thr->stats.scans; thr->stats.scans = 0;
	stats->evals += thr->stats.evals; thr->stats.evals = 0;
	stats->events += thr->stats.events; thr->stats.events = 0;
	stats->subscans += thr->stats.subscans; thr->stats.subscans = 0;
	stats->yields += thr->stats.yields; thr->stats.yields = 0;

	return 0;
}

int
pml_init(void)
{
	/* env */
	bpf_register(pml_vm_env,        0);
	bpf_register(pml_vm_env_get,    1);
	bpf_register(pml_vm_env_set,    2);
	bpf_register(pml_vm_env_del,    3);

	/* int */
	bpf_register(pml_vm_int_str,    4);
	bpf_register(pml_vm_int_bin,    5);
	bpf_register(pml_vm_int_hex,    6);

	/* str */
	bpf_register(pml_vm_str_int,    7);
	bpf_register(pml_vm_str_hex,    8);
	bpf_register(pml_vm_str_join,   9);
	bpf_register(pml_vm_str_split, 10);

	/* bin */
	bpf_register(pml_vm_bin_int,   11);

	/* buf */
	bpf_register(pml_vm_buf_new,   12);
	bpf_register(pml_vm_buf_cmp,   13);
	bpf_register(pml_vm_buf_cat,   14);
	bpf_register(pml_vm_buf_len,   15);

	/* capture */
	bpf_register(pml_vm_cap_add,   16);
	bpf_register(pml_vm_cap_get,   17);

	/* map */
	bpf_register(pml_vm_map_new,   18);
	bpf_register(pml_vm_map_try,   19);
	bpf_register(pml_vm_map_get,   20);
	bpf_register(pml_vm_map_set,   21);
	bpf_register(pml_vm_map_del,   22);
	bpf_register(pml_vm_map_str,   23);
	bpf_register(pml_vm_map_len,   24);

	/* misc */
	bpf_register(pml_vm_peek,      25);
	bpf_register(pml_vm_remaining, 26);
	bpf_register(pml_vm_offset,    27);
	bpf_register(pml_vm_yank,      28);
	bpf_register(pml_vm_skip,      29);
	bpf_register(pml_vm_bstoh,     30);
	bpf_register(pml_vm_lstoh,     31);
	bpf_register(pml_vm_htobs,     32);
	bpf_register(pml_vm_macaddr,   33);
	bpf_register(pml_vm_pton,      34);
	bpf_register(pml_vm_ntop,      35);
	bpf_register(pml_vm_print,     36);
	bpf_register(pml_vm_scan,      37);
	bpf_register(pml_vm_shmr,      38);
	bpf_register(pml_vm_shmw,      39);
	bpf_register(pml_vm_yield,     40);
	bpf_register(pml_vm_advance,   41);
	bpf_register(pml_vm_hash,      42);
	bpf_register(pml_vm_bytes,     43);
	bpf_register(pml_vm_scans,     44);
	bpf_register(pml_vm_limit,     45);

	return 0;
}
