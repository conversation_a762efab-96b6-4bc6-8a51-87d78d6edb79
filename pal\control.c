#include <time.h>

#include <pml/pml.h>

#include "module.h"
#include "control.h"
#include "packet.h"
#include "ct.h"

#define CONTROL_TABLE_STATE (1)

struct pml *ruleset_pml;
uint32_t control_flags = 0;

static void
ctxhtobe(struct context *ctx)
{
	ctx->szone = htobe16(ctx->szone);
	ctx->dzone = htobe16(ctx->dzone);
	ctx->group = htobe16(ctx->group);
	ctx->user = htobe32(ctx->user);
	ctx->host = htobe64(ctx->host);
	ctx->alp = htobe16(ctx->alp);
	ctx->app = htobe16(ctx->app);
	ctx->dev = htobe16(ctx->dev);
	ctx->date = htobe16(ctx->date);
	ctx->threat = htobe16(ctx->threat);
	ctx->geo = htobe16(ctx->geo);
	ctx->cat = htobe16(ctx->cat);
}

static void
ctxbetoh(struct context *ctx)
{
	ctx->szone = be16toh(ctx->szone);
	ctx->dzone = be16toh(ctx->dzone);
	ctx->group = be16toh(ctx->group);
	ctx->user = be32toh(ctx->user);
	ctx->host = be64toh(ctx->host);
	ctx->alp = be16toh(ctx->alp);
	ctx->app = be16toh(ctx->app);
	ctx->dev = be16toh(ctx->dev);
	ctx->date = be16toh(ctx->date);
	ctx->threat = be16toh(ctx->threat);
	ctx->geo = be16toh(ctx->geo);
	ctx->cat = be16toh(ctx->cat);
}

void control(struct packet *packet)
{
	struct conn *conn = packet->conn;
	/* Convert the timestamp, in ms, back to seconds. */
	uint64_t ts = packet->ts / 1000;
	struct tm *tm = localtime((time_t *)&(ts));
	conn->ctx.date = pml_datetime(tm->tm_wday, tm->tm_hour, tm->tm_min);
	/* Convert host order items in the context to big endian */
	ctxhtobe(&conn->ctx);
	pml_eval(ruleset_pml, CONTROL_TABLE_STATE, (void *)&conn->ctx, sizeof(conn->ctx), 0, &conn->ctx);
	/* Put them back into host order */
	ctxbetoh(&conn->ctx);
	conn->flags &= ~F_CONTROL;
}

int
control_init()
{
	if (pml_load(&ruleset_pml, "control.bin") < 0) {
		fprintf(stderr, "pml_load(control) failed\n");
		return -1;
	}
	control_flags = F_CONTROL;
	return pml_main(ruleset_pml, NULL);
}

void
control_exit()
{
	if (ruleset_pml)
		pml_exit(ruleset_pml);
	control_flags = 0;
}

module_init(control_init);
module_exit(control_exit);
