#ifndef PML_H
#define PML_H

#include <stdint.h>

enum pml_type {
	PML_DELETED = -1,
	PML_EMPTY = 0,
	PML_INT = 1,
	PML_BUF = 2,
	PML_MAP = 3,
	PML_PTR = 4
};

struct pml_obj {
	enum pml_type type;
	int16_t offset;
	int16_t length;
	uint32_t expires;
	union {
		int64_t	i;
		struct pml_buf *b;
		struct pml_map *m;
		void *p;
	} v;
};

struct pml_buf {
	uint32_t ref;
	uint32_t size:31;
	uint32_t free:1;
	uint8_t *data;
	uint8_t _data[];
};

struct pml_map {
	uint32_t ref;
	uint32_t size;
	uint32_t cap;
	uint32_t index;
	struct pml_obj *slots;
	struct pml_map *parent;
};

struct pml_stats {
       uint64_t syncs;
       uint64_t scans;
       uint64_t evals;
       uint64_t events;
       uint64_t subscans;
       uint64_t yields;
};

struct pml_thr {
	uint32_t ref;
	uint32_t bpf_size;
	void *bpf_data;
	struct pml_map *sym;
	struct pml_map *mem;
	struct pml_map *sub;
	struct pml_buf *buf;
	struct pml_stats stats;
};

struct pml {
	uint8_t yield;
	uint8_t lang:4;
	uint8_t rscan:1;
	uint8_t block:1;
	uint8_t unused:2;
	uint32_t value;
	uint32_t limit;
	uint64_t resume;
	uint32_t bytes[2];
	uint16_t scans[2];

	struct pml_obj buf;   /* buffer under inspection */
	struct pml_map *map;  /* global environment */
	struct pml_map *gc;   /* temporary hack to address missing delete instructions */
	struct pml_thr *thr;
};

enum pml_match_lang {
	PML_MATCH_NORMAL,
	PML_MATCH_EXTEND
};

/* pml_init()
 *
 * Initializes the library for use.
 *
 * Returns 0 on success, or a negative errno indicating failure.
 */
int pml_init();

/* pml_load()
 *
 * Loads the program from disk and returns a pml handle.
 *
 * Returns 0 on success, or a negative errno indicating failure.
 */
int pml_load(struct pml **pml, const char *program);

/* pml_main()
 *
 * Call the programs main entry point, passing a pointer to the context the
 * pml program expects. The @param ctx is opaque data to this library, but
 * MUST be correctly defined with the compiled pml program.
 *
 * Returns the pml program return value cast to an int.
 */
int pml_main(struct pml *pml, void *ctx);

/* pml_scan()
 *
 * Sends data to a pml pattern matching program.
 *
 * On error, pml_scan() returns a negative errno indicating the reason for the
 * failure. Success is indicated with a non-negative return value. When the
 * scan is complete, pml_scan() returns 0.
 */
int pml_scan(struct pml *pml, const uint8_t *data, uint32_t len, enum pml_match_lang lang, void *ctx);

/* pml_eval()
 *
 * Evalutes a pml table with the given data.
 *
 * On error, pml_eval() returns a negative errno indicating the reason for the
 * failure. Success is indicated with a non-negative return value. When the
 * scan is complete, pml_eval() returns 0.
 */
int pml_eval(struct pml *pml, int state, const uint8_t *data, uint32_t len, uint32_t off, void *ctx);

/* pml_exit()
 *
 * Terminate a pml program.
 *
 * Returns 0 on success, and a negative errno on failure.
 */
int pml_exit(struct pml *pml);

/* pml_clone()
 *
 * When an application want to execute multiple instances of a pml program,
 * pml_clone() is the most efficient method to do so. If the pml program was
 * designed for sharing, the cloned child program will shared data with its
 * parent.
 */
int pml_clone(struct pml **pmlp, struct pml *parent);

/* pml_nameof()
 *
 * Resolves the name for a given type id.
 *
 * Returns the length of the string pointed to by name. If the index does not
 * have a corresponding string, the return code is -EINVAL. When index is out
 * of bounds, returns -ERANGE.
 */
int pml_nameof(struct pml *pml, uint32_t index, const char **name);

/* pml_datetime()
 *
 * Convert the given time to a format which can be used in ruleset evaluation.
 *
 * Returns the number of minutes since Sunday at 00:00, in host-order.
 */
uint16_t pml_datetime(uint8_t wday, uint8_t hour, uint8_t min);

/* pml_listen()
 *
 * Register to receive shared messages generated by the application.
 *
 * Returns 0 on success, and a negative errno on failure.
 */
typedef int (*pml_replication)(const char *data, uint32_t len);
int pml_listen(pml_replication handler);

/* pml_sync()
 *
 * Publish data received from pml_listen() to another pml instance.
 *
 * Returns 0 on success, and a negative errno on failure.
 */
int pml_sync(struct pml *pml, const char *data, uint32_t len);


#define WATCH_INT 1
#define WATCH_STR 2
#define WATCH_OBJ 3

/* pml_watchpoint()
 *
 * Register a callback to handle when the specified memory location is written.
 *
 * Returns a positive integer representing the offset of the value in memory,
 * and a negative errno if name was not found.
 *
 * Supported watch value types are:
 *
 *  INT(1): signed 64bit integer.
 *  STR(2): 8-bit clean byte array; may contain non-printable, embedded 0, etc.
 *  OBJ(3): object, JSON encoded.
 *
 */
typedef void (*pml_watcher)(void *ctx, int key, int type, uint32_t length, const void *value);
int pml_watchpoint(struct pml *pml, const char *name, pml_watcher cb);

/* pml_stats()
 *
 * Collect statistics. Counters are accumulated and can be reset by
 * the caller.
 *
 * Returns 0 on success, and a negative errno on failure.
 */
int pml_stats(struct pml *pml, struct pml_stats *stats);

/* These exist to support the compiler and may be removed */
struct pml_obj pml_str_dup(const char *s);
void pml_gc_collect(struct pml_map *gc, struct pml_obj *val);

int pml_exec(struct pml **pmlp, const void *bpf_data, uint32_t bpf_size, uint64_t *res);

#endif
