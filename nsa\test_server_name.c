/**
 * @file test_server_name.c
 * @brief Simple test to verify server_name watchpoint functionality
 * 
 * This test verifies that the NSA server_name watchpoint implementation
 * works correctly by simulating the callback mechanism.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stddef.h>
#include <assert.h>
#include <stdbool.h>
#include <stdint.h>

/* Mock NSA structures for testing */
struct nsa_pml_context {
    uint8_t src[16];
    uint8_t dst[16];
    uint16_t sport;
    uint16_t dport;
    uint8_t proto;
    uint8_t verdict;
    uint16_t szone;
    uint16_t dzone;
    uint16_t group;
    uint32_t user;
    uint64_t host;
    uint8_t device[6];
    uint16_t alp;
    uint16_t app;
    uint16_t dev;
    uint16_t geo;
    uint16_t threat;
    uint16_t cat;
    uint16_t date;
} __attribute__((packed));

typedef struct nsa_session_context {
    void *pml_classify_instance;
    void *pml_rules_instance;
    struct nsa_pml_context pml_context;
    uint32_t rule_state;
    uint64_t last_analysis_time;
    uint8_t threat_level;
    uint16_t app_classification;
    uint16_t category;
    uint32_t packets_analyzed;
    uint64_t bytes_analyzed;
    uint8_t analysis_complete;
    char app_name[16];
    char alp_name[16];
    char server_name[256];  /* This is what we're testing - now a fixed array */
    bool first_packet_reversed;
} nsa_session_context_t;

/* Mock PML watchpoint constants */
#define WATCH_STR 2

/* Mock logging */
#define NSA_LOG_WARNING(fmt, ...) printf("[WARN] " fmt "\n", ##__VA_ARGS__)
#define NSA_LOG_DEBUG(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)

/**
 * @brief Test implementation of server_name watchpoint callback
 * 
 * This is a copy of the actual implementation from nsa_session.c
 */
static void nsa_watch_server_name(void *ctx, int key, int type, uint32_t length, const void *value) {
    /* Calculate the offset to get back to the nsa_session_context from pml_context */
    nsa_session_context_t *nsa_ctx = (nsa_session_context_t *)((char *)ctx - offsetof(nsa_session_context_t, pml_context));

    (void)key; /* Unused parameter */

    if (type == WATCH_STR && value && length > 0) {
        /* Calculate safe copy length to avoid buffer overflow */
        size_t copy_len = (length < sizeof(nsa_ctx->server_name) - 1) ? length : sizeof(nsa_ctx->server_name) - 1;

        /* Copy the server name and null terminate */
        memcpy(nsa_ctx->server_name, value, copy_len);
        nsa_ctx->server_name[copy_len] = '\0';

        if (length >= sizeof(nsa_ctx->server_name)) {
            NSA_LOG_WARNING("Server name truncated from %u to %zu characters: %.*s...",
                          length, copy_len, (int)copy_len, (const char *)value);
        } else {
            NSA_LOG_DEBUG("Server name updated: %.*s", (int)length, (const char *)value);
        }
    } else {
        NSA_LOG_DEBUG("Unexpected watchpoint callback: type=%d, length=%u", type, length);
    }
}

/**
 * @brief Test the server_name watchpoint functionality
 */
int test_server_name_watchpoint(void) {
    nsa_session_context_t test_ctx;
    const char *test_hostname = "www.example.com";
    const char *test_hostname2 = "api.github.com";
    
    printf("=== Testing NSA server_name watchpoint functionality ===\n");
    
    /* Initialize test context */
    memset(&test_ctx, 0, sizeof(test_ctx));

    printf("1. Testing initial server_name callback with '%s'\n", test_hostname);

    /* Simulate PML watchpoint callback */
    nsa_watch_server_name(&test_ctx.pml_context, 0, WATCH_STR, strlen(test_hostname), test_hostname);

    /* Verify the result */
    if (test_ctx.server_name[0] == '\0') {
        printf("FAIL: server_name is empty after callback\n");
        return -1;
    }
    
    if (strcmp(test_ctx.server_name, test_hostname) != 0) {
        printf("FAIL: server_name mismatch. Expected '%s', got '%s'\n", test_hostname, test_ctx.server_name);
        return -1;
    }
    
    printf("PASS: server_name correctly set to '%s'\n", test_ctx.server_name);
    
    printf("2. Testing server_name update with '%s'\n", test_hostname2);
    
    /* Test updating with a different hostname */
    nsa_watch_server_name(&test_ctx.pml_context, 0, WATCH_STR, strlen(test_hostname2), test_hostname2);
    
    if (strcmp(test_ctx.server_name, test_hostname2) != 0) {
        printf("FAIL: server_name update failed. Expected '%s', got '%s'\n", test_hostname2, test_ctx.server_name);
        return -1;
    }
    
    printf("PASS: server_name correctly updated to '%s'\n", test_ctx.server_name);
    
    printf("3. Testing invalid callback parameters\n");
    
    /* Test with invalid type */
    nsa_watch_server_name(&test_ctx.pml_context, 0, 999, 5, "test");
    /* Should still have the previous value */
    if (strcmp(test_ctx.server_name, test_hostname2) != 0) {
        printf("FAIL: server_name changed unexpectedly after invalid callback\n");
        return -1;
    }
    
    printf("PASS: server_name unchanged after invalid callback\n");

    printf("4. Testing hostname truncation with very long name\n");

    /* Create a hostname longer than 256 characters */
    char long_hostname[300];
    memset(long_hostname, 'a', sizeof(long_hostname) - 1);
    long_hostname[sizeof(long_hostname) - 1] = '\0';

    /* Test with very long hostname */
    nsa_watch_server_name(&test_ctx.pml_context, 0, WATCH_STR, strlen(long_hostname), long_hostname);

    /* Verify truncation */
    if (strlen(test_ctx.server_name) != 255) {  /* 256 - 1 for null terminator */
        printf("FAIL: Long hostname not properly truncated. Length: %zu\n", strlen(test_ctx.server_name));
        return -1;
    }

    printf("PASS: Long hostname properly truncated to %zu characters\n", strlen(test_ctx.server_name));

    printf("=== All tests passed! ===\n");
    return 0;
}

int main(void) {
    return test_server_name_watchpoint();
}
