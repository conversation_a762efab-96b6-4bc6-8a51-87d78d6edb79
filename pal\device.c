#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <assert.h>
#include <time.h>

#include <pml/pml.h>

#include "device.h"
#include "packet.h"
#include "ct.h"

#include "filter.h"
#include "classify.h"
#include "control.h"
#include "report.h"

static struct list_head list = { .next = &list, .prev = &list };

clock_gettime_t pal_gettime = clock_gettime;

int
pml_gettime()
{
	struct timespec ts;
	pal_gettime(CLOCK_MONOTONIC, &ts);
	return (ts.tv_sec + ts.tv_nsec / 1e9);
}

enum {
	VERDICT_ACCEPT = 0
};

static void
device_rx(EV_P_ ev_io *ev, int revents)
{
	(void) revents;
	struct device *dev = ev->data;
	unsigned v[64], i, n = dev->receive(dev, v, 64);

	if (!n) {
		ev_break (EV_A_ EVBREAK_ALL);
		dev->rx_drops++;
		return;
	}

	for (i = 0; i < n; i++) {
		struct packet packet;
		struct conn *conn = NULL;
		struct flow_key key = {0};
		uint8_t dir;
		const uint8_t *start;
		dev->get_packet(dev, v[i], &packet);

		dev->rx_packets++;
		dev->rx_bytes += packet.len;
		start = packet.data;

		if (packet_parse(&packet, &key)) {
			if (ct_upsert(&key, &dir, &conn, packet.ts)) {
				conn->first_pkt = dev->rx_packets;
				conn->flags |= F_REPORT;
				memcpy(&conn->ctx.device, start + 6, sizeof(conn->ctx.device));
				if (conn->ctx.verdict == VERDICT_ACCEPT)
					classify_start(conn);
			}

			packet.conn = conn;
			packet.dir = !!dir;

			if (conn->flags & F_CLASSIFY) {
				classify(&packet);
			}
			if (conn->flags & F_CONTROL) {
				control(&packet);
			}
			if (conn->flags & F_REPORT) {
				report(&packet);
			}

			//printf("conn: %p, dir: %d len: %d\n", conn, dir, packet.len);
			//packet_dump(&packet);
		}
	}

	dev->release(dev, v, n);
}

static void
device_timer(EV_P_ ev_timer *ev, int revents)
{
	(void) revents;

	struct device *dev = ev->data;
	dev->periodic(dev);
	ct_expire(dev->ts);
}

static int
device_fd(struct device *dev)
{
	return dev->get_selectable_fd(dev);
}

int
register_device(struct device *dev)
{
	list_insert(&list, &dev->list);

	dev->timer.data = dev;
	ev_timer_init(&dev->timer, device_timer, 1, 1);
	ev_timer_start(EV_DEFAULT, &dev->timer);

	dev->io.data = dev;
	ev_io_init(&dev->io, device_rx, device_fd(dev), EV_READ);
	ev_io_start(EV_DEFAULT, &dev->io);
	return 0;
}

void
unregister_device(struct device *dev)
{
	list_remove(&dev->list);
}
