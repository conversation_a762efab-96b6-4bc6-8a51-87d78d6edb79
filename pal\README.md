# PAL

PAL is the PML runtime, demonstrating integration of several different drivers with libpml given a compiled signature bundle.

## Getting Started

### Dependencies
* [pml](http://stash.calix.local/projects/CYB/repos/pml/browse)
* [pmlc](http://stash.calix.local/projects/CYB/repos/pmlc/browse)
* [signatures](http://stash.calix.local/projects/CYB/repos/signatures/browse)

### Installing
```
> mkdir build && cd build
> cmake -DCMAKE_BUILD_TYPE=Release .. && make -j && make install
```
Then checkout and build signatures

### Running PAL with PCAP file
From a directory containing built signatures
```
pal -d pcap:<PATH TO PCAP FILE>
```
example output with each connection identified by a new ID:
```
{ "id": 144, "smac": "ca:e6:f3:2c:a9:1a", "proto": 17, "alp": "quic", "app": "nil", "src": "::ffff:192.168.1.204[38370]", "dst"...
{ "id": 261, "smac": "ca:e6:f3:2c:a9:1a", "proto": 6, "alp": "tls", "app": "google", "src": "::ffff:192.168.1.204[45752]", "dst"...
```

### Running PAL with different signatures path
```
pal <PATH TO SIGNATURES BIN> -d pcap:<PATH TO PCAP FILE>
```

### Running PAL on interface passively
```
pal -d pcap:<INTERFACE> -i
>>
>> ct show (then go do something that causes traffic to show up on INTERFACE)
>> ct show
{ "id": 2, "smac": "4a:0f:95:ab:4d:b3", "proto": 6, "alp": "http", "app": "nil", "src": "::ffff:10.88.0.2[54384]", "dst"...
{ "id": 1, "smac": "4a:0f:95:ab:4d:b3", "proto": 17, "alp": "dns", "app": "nil", "src": "::ffff:10.88.0.2[33455]", "dst"...
```