cmake_minimum_required(VERSION 3.10)

project(test VERSION 0.0.1)

# Define a custom build type for Asan
set(CMAKE_C_FLAGS_ASAN "-g -O0 -fsanitize=address,undefined -fno-omit-frame-pointer")
set(CMAKE_CONFIGURATION_TYPES "$CMAKE_CONFIGURATION_TYPES,Asan")
set(CMAKE_C_FLAGS_ASAN "${CMAKE_C_FLAGS_ASAN}" CACHE STRING "Flags for Asan build type" FORCE)
set(CMAKE_EXE_LINKER_FLAGS_ASAN "${CMAKE_EXE_LINKER_FLAGS_ASAN} -fsanitize=address,undefined" CACHE STRING "Linker flags for Asan build type" FORCE)

add_executable(test test.c ../src/bpf.c)

target_include_directories(test PRIVATE ../include)
