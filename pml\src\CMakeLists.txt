cmake_minimum_required(VERSION 3.10)

add_library(pml SHARED bpf.c pml.c)
set_target_properties(pml PROPERTIES VERSION ${PROJECT_VERSION} SOVERSION ${PROJECT_VERSION_MAJOR} OUTPUT_NAME "pml")

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -Wno-override-init")

# Define a custom build type for Asan
set(CMAKE_C_FLAGS_ASAN "-g -O0 -fsanitize=address,undefined -fno-omit-frame-pointer")
set(CMAKE_CONFIGURATION_TYPES "$CMAKE_CONFIGURATION_TYPES,Asan")
set(CMAKE_C_FLAGS_ASAN "${CMAKE_C_FLAGS_ASAN}" CACHE STRING "Flags for Asan build type" FORCE)
set(CMAKE_EXE_LINKER_FLAGS_ASAN "${CMAKE_EXE_LINKER_FLAGS_ASAN} -fsanitize=address,undefined" CACHE STRING "Linker flags for Asan build type" FORCE)

target_include_directories(pml PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/../include)

install(FILES ../include/pml.h ../include/bpf.h DESTINATION include/pml)
install(TARGETS pml LIBRARY DESTINATION lib)

execute_process(
	COMMAND ${CMAKE_C_COMPILER} -dumpmachine
	OUTPUT_VARIABLE target
	OUTPUT_STRIP_TRAILING_WHITESPACE
)

set(CPACK_GENERATOR "DEB")
set(CPACK_DEBIAN_PACKAGE_MAINTAINER "Austen")
set(CPACK_SYSTEM_NAME ${target})
include(CPack)
